/* Global Resets and Base Styles */
:root {
    --winia-bg-color: #C0C0C0; /* Classic Gray */
    --winia-desktop-bg: #008080; /* Teal */
    --winia-button-shadow: #808080; /* Dark gray for shadows */
    --winia-button-dark-shadow: #404040; /* Darker shadow for deeper inset/outset */
    --winia-button-highlight: #FFFFFF; /* White for highlights */
    --winia-button-light-highlight: #DFDFDF; /* Lighter gray for subtle highlights */
    --winia-button-text: #000000;
    --winia-active-title-bar-bg: #000080; /* Navy */
    --winia-active-title-bar-text: #FFFFFF;
    --winia-inactive-title-bar-bg: #808080; /* Mid Gray for inactive */
    --winia-inactive-title-bar-text: #C0C0C0; /* Light Gray text for inactive */
    --winia-progress-bar-color: #000080; /* Navy for progress bar */
    --winia-error-text: #FF0000; /* Red for error messages */
    --codegenie-speech-bubble-bg: #FFFFE1; /* Pale yellow, like <PERSON><PERSON><PERSON> */
}

body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: var(--winia-desktop-bg); /* Desktop BG is set here directly */
    user-select: none;
    font-family: Tahoma, "MS Sans Serif", Arial, sans-serif; /* MOVED */
    font-size: 12px; /* MOVED */
    color: var(--winia-button-text); /* MOVED */
}

/* --- Custom Cursor Classes --- */
.cursor-default { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDNMMTkgMTUgTDExIDE2IEw4LjUgMTMuNSBaIi8+PC9zdmc+') 0 0, default; }
.cursor-hand { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik04IDNhMiAyIDAgMCAxIDItMmg0YzEuMSAwIDIgLjkgMiAybC0zIDVoMnY3SDhWOGgybC0zLTVaIi8+PC9zdmc+') 8 0, pointer; }
.cursor-text { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMCA0VjIwTTEyIDRIMTJNMTIgMjBIMTBabTYtMTZoLTRtNC00aC00Ii8+PC9zdmc+') 8 12, text; } /* CORRECTED THIS LINE */
.cursor-move { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDVIMTNMOSAxMyA1IDlWMTFMMiAxMkw1IDE1TDkgMTNMMyAxOUw3IDIwTDEzIDE0TDIwIDdMMTkgM0wxMyA5TDExIDVaIi8+PC9zdmc+') 12 12, move; }
.cursor-grabbing, .cursor-grabbing-global, .cursor-grabbing-global * { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik05IDhjLTMgMC00IDEtNCA0djJjMCAyIDEgMyAzIDNoMTBjMiAwIDMtMSAzLTN2LTJjMC0zLTEtNC00LTRoLTZ6TTMgMTBoNHYtMkg0djJ6TTE2IDEwaDR2LTJIMTZ2MnoiLz48L3N2Zz4=') 12 12, grabbing !important; }
.cursor-resize-n { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDZIMTlMMTcgMTJMMTkgMTlMMTUgMjJMMTIgMTlMOSAxNkwxMiA5WiIvPjxwYXRoIGQ9Ik0xMiAyVjIyTTkgNkwxMiAyTDE1IDZNOSAxOEwxMiAyMkwxNSAxOFoiLz48L3N2Zz4=') 12 12, ns-resize; }
.cursor-resize-s { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDZIMTlMMTcgMTJMMTkgMTlMMTUgMjJMMTIgMTlMOSAxNkwxMiA5WiIvPjxwYXRoIGQ9Ik0xMiAyVjIyTTkgNkwxMiAyTDE1IDZNOSAxOEwxMiAyMkwxNSAxOFoiLz48L3N2Zz4=') 12 12, ns-resize; }
.cursor-resize-e { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yIDEybDMgM2gtNmwzLTN6TTIyIDEybC0zIDNoNmwtMy0zeiIvPjxwYXRoIGQ9Ik0yIDEySDIyTTYgOWwyIDEybDIgMTUgNmw2IDlsMiAxOGwtNi0zem0wIDBsNiAzLTE4IDNsMTggM3oiLz48L3N2Zz4=') 12 12, ew-resize; }
.cursor-resize-w { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yIDEybDMgM2gtNmwzLTN6TTIyIDEybC0zIDNoNmwtMy0zeiIvPjxwYXRoIGQ9Ik0yIDEySDIyTTYgOWwyIDEybDIgMTUgNmw2IDlsMiAxOGwtNi0zem0wIDBsNiAzLTE4IDNsMTggM3oiLz48L3N2Zz4=') 12 12, ew-resize; }
.cursor-resize-ne { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yMCA0bC0zIDNIMjFWM0gyMHYxek00IDIwbDMtM0gzVjIxSDN2LTF6Ii8+PHBhdGggZD0iTTIwIDRMNCAyME0xNyAzbDMtMy0zLTMtMyAzIDMgM3pNNyAyMWwtMyAzIDEzLTEzIDMtMy0yLjUgMi41eiIvPjwvc3ZnPg==') 12 12, nesw-resize; }
.cursor-resize-sw { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yMCA0bC0zIDNIMjFWM0gyMHYxek00IDIwbDMtM0gzVjIxSDN2LTF6Ii8+PHBhdGggZD0iTTIwIDRMNCAyME0xNyAzbDMtMy0zLTMtMyAzIDMgM3pNNyAyMWwtMyAzIDEzLTEzIDMtMy0yLjUgMi41eiIvPjwvc3ZnPg==') 12 12, nesw-resize; }
.cursor-resize-nw { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDRsMyAzSDNWM0g0djF6TTIwIDIwbC0zLTNIMjFWMjFIMjB2LTF6Ii8+PHBhdGggZD0iTTQgNEwyMCAyME03IDNsMy0zIDMtMyAzIDMtMyAzek0xNyAyMWwtMyAzLTEzLTEzIDMtMyAyLjUgMi41eiIvPjwvc3ZnPg==') 12 12, nwse-resize; }
.cursor-resize-se { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDRsMyAzSDNWM0g0djF6TTIwIDIwbC0zLTNIMjFWMjFIMjB2LTF6Ii8+PHBhdGggZD0iTTQgNEwyMCAyME03IDNsMy0zIDMtMyAzIDMtMyAzek0xNyAyMWwtMyAzLTEzLTEzIDMtMyAyLjUgMi41eiIvPjwvc3ZnPg==') 12 12, nwse-resize; }
.cursor-wait, .cursor-wait-global, .cursor-wait-global * { cursor: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik02IDJoMTJsLTYgNloiLz48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik02IDIyaDEybC02LTZaIi8+PHBhdGggZD0iTTYgMnYyLjVMOC41IDhsLTIuNSA0LjVWMTZMMTIgMjBsLTYtNC41VjEzbDIuNS00LjUtMi41LTQuNVYyeiIvPjwvc3ZnPg==') 12 12, wait !important; }
.cursor-none { cursor: none; }

/* Helper Class */
.hidden { display: none !important; }

/* General Button Styling */
.winia-button-classic {
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 0 0 var(--winia-button-highlight) inset, -1px -1px 0 0 var(--winia-button-shadow) inset;
    padding: 3px 8px;
    color: var(--winia-button-text);
    text-align: center;
    min-width: 70px;
}
.winia-button-classic:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
    box-shadow: none;
    padding: 4px 7px 2px 9px; /* Shift text for pressed effect */
}
.winia-button-classic:disabled {
    color: var(--winia-button-shadow);
    text-shadow: 1px 1px 0 var(--winia-button-light-highlight);
    cursor: default;
}


/* Input Field Styling */
input[type="text"],
input[type="password"],
textarea,
select {
    border: 1px solid;
    border-color: var(--winia-button-shadow) var(--winia-button-highlight) var(--winia-button-highlight) var(--winia-button-shadow);
    background-color: var(--winia-button-highlight); /* White */
    padding: 3px 4px;
    font-family: inherit;
    font-size: inherit;
    color: #000;
    box-shadow: 1px 1px 0 0 var(--winia-button-dark-shadow) inset;
}
input[type="color"] {
    padding: 0;
    border: 1px solid;
    border-color: var(--winia-button-shadow) var(--winia-button-highlight) var(--winia-button-highlight) var(--winia-button-shadow);
    box-shadow: 1px 1px 0 0 var(--winia-button-dark-shadow) inset;
    height: 22px; /* Consistent height */
    width: 35px;
    background-color: var(--winia-button-highlight);
}
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2025%2025%22%20fill%3D%22none%22%20stroke%3D%22black%22%20stroke-width%3D%222%22%20stroke-linecap%3D%22square%22%20stroke-linejoin%3D%22round%22%3E%3Cpath%20d%3D%22M6%209l6%206%206-6%22/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 5px center;
    background-size: 12px 12px;
    padding-right: 20px;
}
textarea {
    resize: vertical;
}

/* Desktop Layout */
#winia-desktop {
    height: calc(100% - 30px); /* Taskbar is 30px */
    position: relative;
    overflow: hidden;
    background-color: var(--winia-desktop-bg);
    background-size: cover;
    background-position: center;
    padding: 10px; /* For desktop icons */
    box-sizing: border-box;
    display: flex;
    flex-direction: column; /* Icons flow vertically first */
    flex-wrap: wrap;      /* Then wrap horizontally */
    align-content: flex-start;
    gap: 5px 15px; /* Row gap, Column gap */
}

/* Desktop Icons */
.desktop-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 75px; /* Fixed width for better alignment */
    padding: 5px 2px;
    text-align: center;
    color: var(--winia-button-highlight); /* White text for desktop icons */
    text-shadow: 1px 1px 1px #00000080;
    border: 1px dotted transparent; /* For focus indication */
}
.desktop-icon img {
    width: 32px;
    height: 32px;
    margin-bottom: 3px;
    object-fit: contain;
}
.desktop-icon span {
    font-size: 11px;
    line-height: 1.3em;
    max-height: 2.6em; /* Allow approx two lines */
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word; /* Break long words if needed */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.desktop-icon:focus, .desktop-icon.selected {
    background-color: rgba(0, 0, 128, 0.5); /* Navy with transparency */
    border: 1px dotted var(--winia-button-highlight);
    outline: none;
}


/* Taskbar */
#winia-taskbar {
    height: 30px;
    background-color: var(--winia-bg-color);
    border-top: 1px solid var(--winia-button-light-highlight);
    box-shadow: 0 -1px 0 var(--winia-button-shadow); /* Simulates a slight raise */
    display: flex;
    align-items: stretch; /* Make items fill height */
    padding: 2px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-sizing: border-box;
}

#winia-start-button {
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 0 0 var(--winia-button-highlight) inset, -1px -1px 0 0 var(--winia-button-shadow) inset;
    color: var(--winia-button-text);
    padding: 0px 8px;
    margin-right: 3px;
    display: flex;
    align-items: center;
    font-weight: bold;
}
#winia-start-button:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
    box-shadow: none;
}
.start-button-logo {
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

#winia-taskbar-app-buttons {
    display: flex;
    flex-grow: 1;
    align-items: stretch; /* Make buttons fill height */
    overflow: hidden; /* Hide excess buttons for now */
}
.taskbar-app-button {
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 0 0 var(--winia-button-highlight) inset, -1px -1px 0 0 var(--winia-button-shadow) inset;
    color: var(--winia-button-text);
    padding: 0 5px;
    margin: 0 1px;
    display: flex;
    align-items: center;
    min-width: 60px;
    max-width: 150px; /* Prevent buttons from getting too wide */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 11px;
}
.taskbar-app-button.active, .taskbar-app-button:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
    box-shadow: none;
    background-color: #DDD; /* Slightly darker when active */
}
.taskbar-app-button img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
}

#winia-system-tray {
    display: flex;
    align-items: center;
    padding: 0 5px;
    border-left: 1px solid var(--winia-button-shadow);
    border-right: 1px solid var(--winia-button-light-highlight);
    margin-left: 2px;
}
#winia-codegenie-tray-icon {
    filter: drop-shadow(1px 1px 0px var(--winia-button-highlight));
}
#winia-clock {
    padding: 0 5px;
    font-size: 11px;
}

/* Start Menu */
#winia-start-menu {
    position: fixed;
    bottom: 30px; /* Above taskbar */
    left: 0;
    width: 220px;
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 2px 2px 3px rgba(0,0,0,0.4);
    z-index: 1001;
    display: flex;
}
.start-menu-sidebar {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
    padding: 10px 5px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    transform: rotate(180deg);
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
}
.start-menu-sidebar-logo {
    width: 20px;
    height: 20px;
    margin-bottom: 10px;
    filter: drop-shadow(1px 1px 0px rgba(0,0,0,0.3));
}
.start-menu-main {
    flex-grow: 1;
    padding: 3px;
}
#winia-start-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
#winia-start-menu li {
    padding: 5px 8px;
    display: flex;
    align-items: center;
}
#winia-start-menu li:hover, #winia-start-menu li:focus {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
    outline: none;
}
.start-menu-item-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
.start-menu-separator hr {
    border: none;
    border-top: 1px solid var(--winia-button-shadow);
    border-bottom: 1px solid var(--winia-button-light-highlight);
    margin: 3px 0;
}

/* Winia Windows */
.winia-window {
    position: absolute;
    min-width: 150px;
    min-height: 100px;
    border-top: 1px solid var(--winia-button-light-highlight);
    border-left: 1px solid var(--winia-button-light-highlight);
    border-right: 1px solid var(--winia-button-dark-shadow);
    border-bottom: 1px solid var(--winia-button-dark-shadow);
    background-color: var(--winia-bg-color); /* This is the client area bg */
    box-shadow: 2px 2px 0px 0px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.winia-window.maximized {
    border: none !important; /* No border when maximized */
    box-shadow: none !important;
}

.winia-window-title-bar {
    background-color: var(--winia-inactive-title-bar-bg);
    color: var(--winia-inactive-title-bar-text);
    padding: 3px 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 20px; /* Fixed height for title bar */
    box-sizing: border-box;
    border-bottom: 1px solid transparent; /* For active state only */
}
.winia-window.active .winia-window-title-bar {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
}
.winia-window-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
}
.winia-window-title {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
}
.winia-window-controls {
    display: flex;
}
.winia-window-controls button {
    background: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    padding: 0px;
    width: 16px; /* Classic small button size */
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 2px;
    box-shadow: 0.5px 0.5px 0 0 var(--winia-button-highlight) inset, -0.5px -0.5px 0 0 var(--winia-button-shadow) inset;
}
.winia-window-controls button:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
    box-shadow: none;
}
.winia-window-controls button img {
    width: 10px;
    height: 10px;
    /* SVG icons should use currentColor if fill/stroke not set, or be black */
}

.winia-window-content {
    flex-grow: 1;
    overflow: auto; /* Important for app content */
    padding: 0; /* Apps themselves should manage padding if needed */
    margin: 2px; /* Margin to show window background around content */
    border: 1px solid;
    border-color: var(--winia-button-shadow) var(--winia-button-highlight) var(--winia-button-highlight) var(--winia-button-shadow);
    background-color: var(--winia-button-highlight); /* Default white content background */
    box-shadow: 0.5px 0.5px 0 0 var(--winia-button-dark-shadow) inset;
    position: relative; /* For absolute positioned children like dialogs */
}

/* Window Resize Handles */
.winia-resize-handle {
    position: absolute;
    background: transparent; /* Handles are invisible */
    z-index: 1; /* Above content, below other windows if any overlap issue */
}
.winia-resize-handle-n { top: -3px; left: 0; right: 0; height: 6px; }
.winia-resize-handle-s { bottom: -3px; left: 0; right: 0; height: 6px; }
.winia-resize-handle-e { top: 0; bottom: 0; right: -3px; width: 6px; }
.winia-resize-handle-w { top: 0; bottom: 0; left: -3px; width: 6px; }
.winia-resize-handle-ne { top: -3px; right: -3px; width: 8px; height: 8px; }
.winia-resize-handle-nw { top: -3px; left: -3px; width: 8px; height: 8px; }
.winia-resize-handle-se { bottom: -3px; right: -3px; width: 8px; height: 8px; }
.winia-resize-handle-sw { bottom: -3px; left: -3px; width: 8px; height: 8px; }

/* Winia Dialog (used by Web Voyager About) */
.winia-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 2px 2px 5px rgba(0,0,0,0.5);
    z-index: 100; /* Above window content */
    min-width: 250px;
}
.winia-dialog-title-bar {
    background-color: var(--winia-active-title-bar-bg); /* Dialogs usually appear active */
    color: var(--winia-active-title-bar-text);
    padding: 3px 5px;
    font-weight: bold;
    text-align: center; /* Dialog titles often centered */
}
.winia-dialog-content {
    padding: 15px;
    text-align: center; /* Center content for simple dialogs */
}
.winia-dialog-content p {
    margin: 0 0 10px 0;
}
.winia-dialog-content button { /* Use classic button styling */
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 0 0 var(--winia-button-highlight) inset, -1px -1px 0 0 var(--winia-button-shadow) inset;
    padding: 3px 12px;
    min-width: 70px;
}
.winia-dialog-content button:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
    box-shadow: none;
}


/* CodeGenie Floating Assistant */
#codegenie-character-host {
    position: fixed;
    bottom: 40px; /* Above taskbar + some padding */
    right: 20px;
    width: 64px;
    height: 64px;
    z-index: 990;
    transition: transform 0.3s ease-out;
}
#codegenie-character-host:hover {
    transform: scale(1.1);
}
#codegenie-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    image-rendering: pixelated; /* For pixel art sprites */
}
#codegenie-speech-bubble {
    position: fixed;
    bottom: 110px; /* Above avatar */
    right: 20px;
    width: 250px;
    background-color: var(--codegenie-speech-bubble-bg);
    border: 1px solid #808080;
    box-shadow: 3px 3px 5px rgba(0,0,0,0.2);
    padding: 10px;
    border-radius: 8px;
    z-index: 991;
    font-size: 13px;
    line-height: 1.4;
}
#codegenie-speech-bubble::after { /* Speech bubble tail */
    content: '';
    position: absolute;
    bottom: -10px;
    right: 30px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid var(--codegenie-speech-bubble-bg);
    filter: drop-shadow(1px 2px 1px rgba(0,0,0,0.1));
}
#codegenie-speech-bubble-text { margin-bottom: 8px; }
#codegenie-speech-bubble-input { width: calc(100% - 10px); padding: 4px; margin-bottom: 8px; }
#codegenie-speech-bubble-actions button {
    margin-right: 5px;
    padding: 3px 8px;
    /* Uses general button styling if .winia-button-classic is applied in TS */
}
#codegenie-progress-container { padding-top: 5px; }
#codegenie-progress-message { font-size: 11px; margin-bottom: 4px; text-align: center; }
#codegenie-progress-bar-outer {
    height: 10px;
    background-color: #E0E0E0;
    border: 1px solid var(--winia-button-shadow);
    padding: 1px;
}
#codegenie-progress-bar-inner {
    height: 100%;
    background-color: var(--winia-progress-bar-color);
    width: 0%;
    transition: width 0.1s linear;
}


/* Notepad Specific Styles */
.notepad-container { display: flex; flex-direction: column; height: 100%; }
.notepad-menu-bar {
    display: flex;
    background-color: var(--winia-bg-color);
    padding: 2px;
    border-bottom: 1px solid var(--winia-button-shadow);
    position: relative; /* For dropdown positioning */
}
.notepad-menu-button {
    background: none; border: none; padding: 2px 6px;
}
.notepad-menu-button:hover { background-color: #DDD; }
.notepad-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0; /* Aligns with the 'File' button usually */
    background-color: var(--winia-bg-color);
    border: 1px solid var(--winia-button-shadow);
    box-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    padding: 2px 0;
    z-index: 10; /* Above textarea */
    min-width: 100px;
}
.notepad-dropdown-item { padding: 3px 10px; }
.notepad-dropdown-item:hover { background-color: var(--winia-active-title-bar-bg); color: var(--winia-active-title-bar-text); }
.notepad-container textarea { flex-grow: 1; border: none; outline: none; resize: none; padding: 5px; width: 100%; height: 100%; box-sizing: border-box;}

/* Winia Explorer Specific Styles */
.explorer-container { display: flex; flex-direction: column; height: 100%; }
.explorer-toolbar {
    padding: 3px;
    border-bottom: 1px solid var(--winia-button-shadow);
    background-color: var(--winia-bg-color);
}
.explorer-toolbar button { margin-right: 3px; padding: 1px 3px; } /* Small buttons */
.explorer-toolbar button img { width: 14px; height: 14px; }

.explorer-address-bar {
    display: flex;
    align-items: center;
    padding: 3px;
    border-bottom: 1px solid var(--winia-button-shadow);
    background-color: var(--winia-bg-color);
}
.explorer-address-bar label { margin-right: 5px; font-size: 11px;}
.explorer-address-input { flex-grow: 1; font-size: 11px; }

.explorer-items-view {
    flex-grow: 1;
    overflow-y: auto;
    padding: 5px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 5px;
}
.explorer-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 65px;
    padding: 4px;
    border: 1px solid transparent;
    text-align: center;
}
.explorer-item:hover { background-color: #E0E0E0; }
.explorer-item:focus, .explorer-item.selected {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
    border: 1px dotted var(--winia-active-title-bar-text);
    outline: none;
}
.explorer-item img { width: 32px; height: 32px; margin-bottom: 2px; }
.explorer-item span {
    font-size: 10px;
    line-height: 1.2em;
    max-height: 2.4em; /* Two lines */
    overflow: hidden;
    word-break: break-all;
}
.explorer-context-menu {
    position: fixed;
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 3px rgba(0,0,0,0.3);
    padding: 2px 0;
    z-index: 1050; /* Above other UI elements */
    min-width: 120px;
}
.explorer-context-menu-item {
    padding: 4px 10px;
    font-size: 11px;
}
.explorer-context-menu-item:hover {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
}

/* Recycle Bin Specific Styles */
.recycle-bin-container { display: flex; flex-direction: column; height: 100%; font-size: 11px; }
.recycle-bin-toolbar {
    padding: 4px;
    border-bottom: 1px solid var(--winia-button-shadow);
    background-color: var(--winia-bg-color);
}
.recycle-bin-toolbar button { margin-right: 5px; padding: 2px 6px; /* Apply .winia-button-classic in TS */ }
.recycle-bin-items-view { flex-grow: 1; overflow: auto; }
.recycle-bin-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr; /* Adjust as needed */
    gap: 5px;
    padding: 4px 6px;
    border-bottom: 1px solid #E0E0E0;
}
.recycle-bin-item.header { font-weight: bold; background-color: #EFEFEF; }
.recycle-bin-item div { display: flex; align-items: center; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
.recycle-bin-item img { width: 16px; height: 16px; margin-right: 4px; }
.recycle-bin-item.selected { background-color: var(--winia-active-title-bar-bg); color: var(--winia-active-title-bar-text); }

/* Control Panel Styles */
.control-panel-main-view {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
    align-items: flex-start;
    justify-content: flex-start;
}
.control-panel-category-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 90px;
    padding: 10px;
    text-align: center;
    background-color: transparent;
    border: 1px solid transparent;
}
.control-panel-category-button:hover, .control-panel-category-button:focus {
    border: 1px solid #DDD;
    background-color: #E8E8E8;
    box-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    outline: none;
}
.control-panel-category-button img { width: 32px; height: 32px; margin-bottom: 8px; }

.settings-view-container { padding: 15px; font-size: 11px; }
.settings-section { margin-bottom: 20px; }
.settings-section-title { font-weight: bold; margin-bottom: 10px; font-size: 13px; border-bottom: 1px solid var(--winia-button-shadow); padding-bottom: 3px; }
.settings-control-row { margin-bottom: 8px; display: flex; align-items: center; gap: 8px; }
.settings-control-row label { display: inline-block; min-width: 150px; }
.settings-control-row input[type="checkbox"] + label { min-width: auto; }

.control-panel-back-button {
    margin-bottom: 15px;
    padding: 3px 8px; /* Apply .winia-button-classic in TS */
}

/* Display Settings - Wallpaper */
.wallpaper-preview-container { display: flex; gap: 10px; margin-bottom: 10px; align-items: flex-start;}
.current-wallpaper-preview {
    width: 160px; height: 120px;
    border: 2px inset var(--winia-button-shadow);
    background-size: cover; background-position: center;
    background-color: var(--winia-desktop-bg); /* Fallback for color wallpapers */
}
.wallpaper-list {
    display: flex; flex-wrap: wrap; gap: 5px;
    max-height: 120px; overflow-y: auto; flex-grow: 1;
    border: 1px solid var(--winia-button-shadow); padding: 5px;
}
.wallpaper-item {
    width: 80px; display: flex; flex-direction: column; align-items: center;
    padding: 3px; border: 1px solid transparent;
}
.wallpaper-item-thumbnail {
    width: 70px; height: 50px;
    border: 1px solid var(--winia-button-shadow);
    margin-bottom: 3px; background-size: cover; background-position: center;
}
.wallpaper-item.selected, .wallpaper-item:hover {
    border-color: var(--winia-active-title-bar-bg);
    background-color: #E8E8F0;
}
.settings-view-container input[type="file"] {
    border: none; box-shadow: none; padding: 0;
}

/* Screensaver Overlay */
#winia-screensaver-overlay {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background-color: black;
    z-index: 10000;
    color: white;
    display: flex; /* For centering if simple text/logo */
    justify-content: center;
    align-items: center;
    overflow: hidden; /* Important for canvas or moving elements */
}
.screensaver-floating-text {
    position: absolute;
    font-size: 48px;
    font-family: "Times New Roman", serif;
    color: #DDD;
    text-shadow: 2px 2px 3px #555;
    white-space: nowrap;
}
.screensaver-stars-canvas {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
}

/* CodeGenie Avatar Settings */
.codegenie-avatar-settings .settings-section-title { margin-bottom: 5px; }
.codegenie-avatar-settings p { font-size: 10px; margin-bottom: 10px; color: #333; }
.codegenie-sprite-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}
.codegenie-sprite-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    border: 1px solid #DDD;
    border-radius: 4px;
    background-color: #F9F9F9;
}
.codegenie-sprite-state-label {
    font-size: 10px;
    font-weight: bold;
    margin-bottom: 5px;
    text-align: center;
}
.codegenie-sprite-preview {
    width: 64px;
    height: 64px;
    border: 1px dashed #CCC;
    margin-bottom: 8px;
    image-rendering: pixelated;
    background-color: #FFF; /* Ensure transparent PNGs are visible */
    object-fit: contain;
}
.codegenie-sprite-input {
    font-size: 10px;
    max-width: 100%;
    border: none !important; box-shadow: none !important; padding: 0 !important;
}
.codegenie-avatar-settings button { /* Apply .winia-button-classic in TS */
    padding: 4px 10px; font-size: 11px;
}


/* Web Voyager Styles */
#voyager-app-container { color: #000; }
.voyager-menu-bar {
    display: flex;
    background-color: var(--winia-bg-color);
    padding: 1px 2px;
    border-bottom: 1px solid var(--winia-button-shadow);
    user-select: none;
    flex-wrap: wrap; /* Allow menus to wrap if not enough space */
}
.voyager-menu { position: relative; }
.voyager-menu-title-button {
    background: none; border: none; padding: 3px 6px; font-size: 11px;
}
.voyager-menu-title-button:hover, .voyager-menu-dropdown.active + .voyager-menu-title-button {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
}
.voyager-menu-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
    padding: 1px 0;
    z-index: 20; /* Above toolbar/address bar */
    min-width: 150px;
    display: none; /* Hidden by default */
}
.voyager-menu-dropdown.active { display: block; }
.voyager-menu-item {
    padding: 3px 10px 3px 20px; /* Space for checkmark */
    font-size: 11px;
    white-space: nowrap;
    position: relative;
}
.voyager-menu-item:hover {
    background-color: var(--winia-active-title-bar-bg);
    color: var(--winia-active-title-bar-text);
}
.voyager-menu-item.disabled { color: var(--winia-button-shadow); background-color: transparent !important; }
.voyager-menu-checkmark { position: absolute; left: 5px; top: 3px; }
.voyager-menu-separator { height: 1px; background-color: var(--winia-button-shadow); margin: 3px 1px; border-bottom: 1px solid var(--winia-button-light-highlight); }

.voyager-toolbar { display: flex; gap: 3px; padding: 3px; border-bottom: 1px solid var(--winia-button-shadow); background-color: var(--winia-bg-color); align-items: center; }
.voyager-toolbar button { padding: 2px 3px; /* Uses small-control-button style from TS */ }
.voyager-toolbar button img { width: 14px; height: 14px; }

#voyager-address-bar-container { display: flex; gap: 4px; padding: 3px; border-bottom: 1px solid var(--winia-button-shadow); background-color: var(--winia-bg-color); align-items: center; flex-grow:0; }
#voyager-address-bar-container label { white-space: nowrap; padding: 0 2px; font-size: 11px; }
#voyager-url-input { flex-grow: 1; padding: 3px 4px; font-family: "MS Sans Serif", Arial, sans-serif; font-size: 11px; }
#voyager-go-button {
    padding: 2px 8px; font-size: 11px;
    /* Use .winia-button-classic from TS or style here */
    background-color: var(--winia-bg-color);
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
}
#voyager-go-button:active {
    border-color: var(--winia-button-dark-shadow) var(--winia-button-light-highlight) var(--winia-button-light-highlight) var(--winia-button-dark-shadow);
}

.voyager-content-wrapper { flex-grow: 1; position: relative; background-color: var(--winia-button-highlight); overflow: hidden; }
#voyager-content-frame { width: 100%; height: 100%; border: none; background-color: var(--winia-button-highlight); }
.voyager-status-overlay {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
    display: flex; flex-direction: column; justify-content: center; align-items: center;
    background-color: var(--winia-button-highlight); /* Page background for overlays */
    font-size: 14px; text-align: center; padding: 20px; box-sizing: border-box; z-index:10;
}
.voyager-status-overlay.hidden { display: none; }
.voyager-error-display { color: var(--winia-error-text); }

.voyager-status-bar {
    padding: 2px 5px;
    font-size: 10px;
    border-top: 1px solid var(--winia-button-shadow);
    background-color: var(--winia-bg-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 16px; /* Fixed height */
    line-height: 16px;
}

/* Winia ID Card Creator */
.winia-id-creator-container {
    display: flex;
    height: 100%;
    padding: 10px;
    gap: 10px;
    background-color: var(--winia-bg-color); /* Match general window bg */
}
.winia-id-creator-form-pane {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--winia-button-shadow);
    background-color: #D4D0C8; /* Lighter gray for form area */
    overflow-y: auto;
}
.winia-id-creator-preview-pane {
    flex: 0 0 260px; /* Fixed width for preview */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    border: 1px solid var(--winia-button-shadow);
    background-color: #B0B0B0; /* Darker gray for preview backdrop */
}
.winia-id-creator-form-row {
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
}
.winia-id-creator-form-row label {
    margin-bottom: 3px;
    font-size: 11px;
    font-weight: bold;
}
.winia-id-creator-form-row input,
.winia-id-creator-form-row select,
.winia-id-creator-form-row textarea {
    width: calc(100% - 10px); /* Full width minus padding */
    font-size: 12px;
}
.winia-id-creator-form-row textarea {
    min-height: 50px;
}
.winia-id-creator-form-pane button { /* Apply .winia-button-classic in TS */
    margin-top: 5px; padding: 4px 10px;
}
.winia-id-creator-status {
    font-size: 10px;
    margin-left: 10px;
    font-style: italic;
}

.winia-id-card-preview {
    width: 240px;
    height: 150px;
    border: 1px solid var(--winia-button-dark-shadow);
    box-shadow: 2px 2px 3px rgba(0,0,0,0.3);
    padding: 10px;
    display: flex;
    flex-direction: column;
    color: #000; /* Default text color on card */
    font-family: "Lucida Console", Monaco, monospace;
    position: relative; /* For absolute positioned elements like title */
}
.winia-id-card-preview-title {
    position: absolute;
    top: 5px;
    right: 8px;
    font-size: 10px;
    font-weight: bold;
    color: rgba(0,0,0,0.6); /* Watermark-like title */
}
.winia-id-card-preview-portrait {
    width: 64px;
    height: 64px;
    border: 2px solid #333;
    margin-bottom: 8px;
    object-fit: contain;
    background-color: rgba(255,255,255,0.3); /* Slight bg for placeholder visibility */
    image-rendering: pixelated;
}
.winia-id-card-preview-info {
    font-size: 11px;
    line-height: 1.4;
}
.winia-id-card-preview-info div {
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Winia ID Card Creator Export Progress */
.winia-id-export-progress-container {
    margin-top: 10px;
    padding: 8px;
    border: 1px solid var(--winia-button-shadow);
    background-color: #E8E8E8; /* Slightly different from form bg for emphasis */
}
.winia-id-export-progress-message {
    font-size: 11px;
    margin-bottom: 5px;
    text-align: center;
    min-height: 1.2em; /* Ensure space even if empty */
}
.winia-id-export-progress-bar-outer {
    height: 12px;
    background-color: #FFF; /* White background for the bar track */
    border: 1px solid var(--winia-button-shadow);
    padding: 1px; /* To make inner bar slightly smaller */
    box-shadow: inset 1px 1px 1px rgba(0,0,0,0.1);
}
.winia-id-export-progress-bar-inner {
    height: 100%;
    background-color: var(--winia-progress-bar-color); /* Navy */
    width: 0%;
    transition: width 0.2s linear; /* Smooth progress update */
}

/* Winia Import Overlay */
#winia-import-overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background-color: var(--winia-desktop-bg); /* Same as desktop background */
    z-index: 20000; /* Above everything */
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.winia-import-container {
    background-color: var(--winia-bg-color);
    padding: 30px 40px;
    border: 1px solid;
    border-color: var(--winia-button-light-highlight) var(--winia-button-dark-shadow) var(--winia-button-dark-shadow) var(--winia-button-light-highlight);
    box-shadow: 2px 2px 5px rgba(0,0,0,0.3),
                inset 1px 1px 0px var(--winia-button-light-highlight), /* Inner highlight top-left */
                inset -1px -1px 0px var(--winia-button-shadow); /* Inner shadow bottom-right */
    text-align: center;
    min-width: 350px;
}

.winia-import-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--winia-active-title-bar-bg); /* Navy title */
}

.winia-import-message {
    font-size: 13px;
    margin-bottom: 25px;
    line-height: 1.5;
}

.winia-import-actions button {
    /* Re-using .winia-button-classic styling - ensure it's applied in TS or define here */
    padding: 8px 20px;
    font-size: 14px;
    margin: 0 10px;
}

#winia-import-status-message {
    margin-top: 20px;
    font-size: 11px;
    font-style: italic;
    color: #333;
    min-height: 1.2em; /* Reserve space */
}
