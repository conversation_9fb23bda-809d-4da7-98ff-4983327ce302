
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Winia</title>
    <link rel="stylesheet" href="index.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🪟</text></svg>">
<script type="importmap">
{
  "imports": {
    "@google/genai": "https://esm.sh/@google/genai@^1.0.1"
  }
}
</script>
<script
  src="https://unpkg.com/@dotlottie/player-component@2.7.12/dist/dotlottie-player.mjs"
  type="module"
></script>
</head>
<body>
    <div id="winia-desktop">
        <!-- Desktop icons will be dynamically added -->
    </div>

    <div id="winia-taskbar">
        <button id="winia-start-button" aria-label="Start Menu" aria-haspopup="true" aria-expanded="false">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNNC41IDEydjZoNnYtNkg0LjV6TTQuNSAxOHYtNmg2djZINGF6TTQuNSAxOHYtNmg2djZINGF6TTEyLjUgMTJoNnY2aC02di02ek0xMi41IDZoNnY2aC02VjZ6TTQuNSA2aDZ2Nkg0LjVWMnoiLz48L3N2Zz4=" alt="Winia Logo" class="start-button-logo">
            <span>Start</span>
        </button>
        <div id="winia-taskbar-app-buttons">
            <!-- Buttons for open/minimized apps will go here -->
        </div>
        <div id="winia-system-tray">
            <img id="winia-codegenie-tray-icon" alt="CodeGenie" aria-label="Open CodeGenie" role="button" tabindex="0" style="width: 16px; height: 16px; margin-right: 5px; cursor: default;">
            <div id="winia-clock" aria-live="polite" aria-atomic="true">00:00</div>
        </div>
    </div>

    <div id="winia-start-menu" role="menu" class="hidden">
        <div class="start-menu-sidebar">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVpbmc9InJvdW5kIj48cGF0aCBkPSJNMyAzdjE4aDExTDIxIDZsLTUtNVptMyAxNWgxNG0tMTQtNGgxMG0tMTAtNGg4Ii8+PC9zdmc+" alt="Winia Logo" class="start-menu-sidebar-logo">
            <div>Winia</div>
        </div>
        <div class="start-menu-main">
            <ul id="winia-start-menu-apps-list">
                <!-- App items will be dynamically added here -->
            </ul>
            <ul>
                <li class="start-menu-separator" role="separator"><hr></li>
                <li role="menuitem" data-action="settings">
                    <!-- Placeholder for actual settings icon -->
                     <img src="" alt="" class="start-menu-item-icon" data-icon-name="CONTROL_PANEL"> Configuración
                </li>
                <li class="start-menu-separator" role="separator"><hr></li>
                <li role="menuitem" data-action="shutdown">
                     <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xOC4zNiA2LjY0YTk5IDAgMSAxLTEyLjcyIDBBMTAgMTAgMCAwIDEgMTIgMnYxMCIvPjwvc3ZnPg==" alt="" class="start-menu-item-icon"> Apagar Sistema...
                </li>
            </ul>
        </div>
    </div>

    <!-- CodeGenie Floating Assistant Elements -->
    <div id="codegenie-character-host" class="hidden">
        <img id="codegenie-avatar-img" src="" alt="CodeGenie Assistant">
    </div>
    <div id="codegenie-speech-bubble" class="hidden">
        <div id="codegenie-speech-bubble-text"></div>
        <div id="codegenie-progress-container" class="hidden">
            <div id="codegenie-progress-message"></div>
            <div id="codegenie-progress-bar-outer">
                <div id="codegenie-progress-bar-inner"></div>
            </div>
        </div>
        <input type="text" id="codegenie-speech-bubble-input" placeholder="Describe your app...">
        <div id="codegenie-speech-bubble-actions">
            <!-- Buttons like Send, Yes, No will go here -->
        </div>
    </div>

    <div id="winia-screensaver-overlay" class="hidden">
        <!-- Screensaver content will be injected here -->
    </div>

    <!-- Winia Import Overlay - ADDED THIS SECTION -->
    <div id="winia-import-overlay" class="hidden">
        <div class="winia-import-container">
            <div class="winia-import-title">Bienvenido a Winia</div>
            <p class="winia-import-message">¿Tienes una Winia ID Card para restaurar tu sesión o quieres empezar una nueva aventura?</p>
            <div class="winia-import-actions">
                <button id="winia-import-card-button" class="winia-button-classic cursor-hand">Importar Winia ID Card</button>
                <button id="winia-new-session-button" class="winia-button-classic cursor-hand">Iniciar Nueva Sesión (en blanco)</button>
            </div>
            <div id="winia-import-status-message"></div>
        </div>
    </div>
    <!-- END OF ADDED SECTION -->

    <script type="module" src="index.tsx"></script>
</body>
</html>
