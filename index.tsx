
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import { GoogleGenAI, GenerateContentResponse } from "@google/genai";

// --- Icon SVGs ---
const ICONS = {
    APP_DEFAULT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiAyTDQgOWg0djhsNG0wLTgtNCA3aDR2OCIvPjwvc3ZnPg==`,
    NOTEPAD: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xNiAySDhjLTEuMSAwLTIgLjktMiAydjE2YzAgMS4xLjkgMiAyIDJoOGMxLjEgMCAyLS45IDItMlY0YzAtMS4xLS45LTItMi0yeiIvPjxwYXRoIGQ9Ik0xMCA0djRoNG0tNGg0bS00IDRoNG0tNCA0aDIiLz48L3N2Zz4=`,
    CODEGENIE_TRAY: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJibGFjayI+PHBhdGggZD0iTTIgMTJoMHYyaDJ2LTJoMnYtMmgyVjhoMnYyaDJWOGgyVjZoMnYyaDJWMmgxLjVMMjQgMHYyMkgwVjEybDJtMi0xaDF2MWgtMXYtMW0xMCAxaDF2MWgtMXYtMW0tOSAzaDF2MWgtMXYtMW0xIDNoMVYxMWgtMXYybS0xIDRoMXYxaC0xdi0xbTEgMmgxVjEyaDZ2LTFtMy0yaDF2MWgtMXYtMW0xIDFoMVYxNWgtMXYybS0xIDNoMVYxN2gtMXYybTEgMmgxVjE3aC0xVjE5TTEwIDE3aDF2MWgtMXYtMW0tMyAxaDF2MWgtMXYtMW0xLTJoMXYxaC0xdi0xbS0xLTJoMVYxM2gtMXYxbTEgMWgxVjExaC0xVjEzTTQgMTNoMVYxMWgtMXYyIi8+PC9zdmc+`,
    MINIMIZE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJibGFjayI+PHJlY3QgeD0iNCIgeT0iMTIiIHdpZHRoPSIxNiIgaGVpZ2h0PSI0Ii8+PC9zdmc+`,
    MAXIMIZE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjQiPjxyZWN0IHg9IjUiIHk9IjUiIHdpZHRoPSIxNCIgaGVpZ2h0PSIxNCIvPjwvc3ZnPg==`,
    RESTORE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjMiPjxyZWN0IHg9IjgiIHk9IjgiIHdpZHRoPSIxMCIgaGVpZ2h0PSIxMCIvPjxwYXRoIGQ9Ik00IDhoNFY0aDEwUTE0IDQgMTQgNE00IDE0aDR2NEg0eiIvPjwvc3ZnPg==`,
    CLOSE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCI+PHBhdGggZD0iTTcgN2wxMCAxME03IDE3bDEwLTEwIi8+PC9zdmc+`,
    MY_COMPUTER: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjMiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PHBhdGggZD0iTTIgMTVoMjBNNiAxOWg0bTEgMGg0Ii8+PGNpcmNsZSBjeD0iNi41IiBjeT0iNy41IiByPSIuNSIgZmlsbD0iY3VycmVudENvbG9yIi8+PGNpcmNsZSBjeD0iMTAuNSIgY3k9IjcuNSIgciI9Ii41IiBmaWxsPSJjdXJyZW50Q29sb3IiLz48L3N2Zz4=`,
    FOLDER_CLOSED: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik00IDZoMTZ2MTJIMHoiLz48cGF0aCBkPSJNMjAgNmwtMi0ySDZhLTEgMSAwIDAgMC0xIDF2MWgxNnoiLz48L3N2Zz4=`,
    FOLDER_OPEN: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik01IDZoMTRsMiAyaC0xOHoiLz48cGF0aCBkPSJNNCAxOFY4aDE2djEwSDR6Ii8+PC9zdmc+`,
    FILE_TEXT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xNCAydjZoNnptLTYgMTZIOHYtNGg0em0wLTZIOHYtNGg0em0wLTZIOHYtNGg0em02IDBINDZ2MTZoMTJWMmg2Ii8+PC9zdmc+`,
    RECYCLE_BIN_EMPTY: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMCAzTDYgN2g0bDQgMEw5IDN6Ii8+PHBhdGggZD0iTTQgN2gxNnYxNEg0eiIvPjxwYXRoIGQ9Ik0xMCAxMWwxIDYtMSAwek0xNCAxMWwtMSA2IDEgMHoiLz48L3N2Zz4=`,
    RECYCLE_BIN_FULL: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMCAzTDYgN2g0bDQgMEw5IDN6Ii8+PHBhdGggZD0iTTQgN2gxNnYxNEg0eiIvPjxwYXRoIGQ9Ik0xMyAxNmwyLTRtLTMtNWwyIDJtLTIgMmwxIDNtLTEgMmg0bS0yLTNoMiIvPjwvc3ZnPg==`,
    ARROW_UP: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiA1djE0bTcgLTdMMTIgNWwtNyA3Ii8+PC9zdmc+`,
    CONTROL_PANEL: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiA0djE2TTggOGgxNkg4em00IDRoOG0wIDRoOCIvPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiLz48L3N2Zz4=`,
    DISPLAY_SETTINGS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjYiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxMiIgcng9IjIiLz48cGF0aCBkPSJNNiAxMmwxLjUtMS41TDkgMTJsLTEuNSAxLjVNNCAxOGgxNkg0eiIvPjwvc3ZnPg==`,
    SOUND_SETTINGS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMSA1TDYgOWgtM2EyIDIgMCAwIDAtMiAydjJhMiAyIDAgMCAwIDIgMmgzbDUgNGgxYTIgMiAwIDAgMCAyLTJWN2EyIDIgMCAwIDAtMi0yeiIvPjxwYXRoIGQ9Ik0xOSA4YTQuNSA0LjUgMCAwIDEgMCA4Ii8+PC9zdmc+`,
    CODEGENIE_SETTINGS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xNS41IDguNUMxNS41IDUuNSAxMi41IDMgOS41IDNTMy41IDUuNSAzLjUgOC41IDYuNSAxNCA5LjUgMTRzNi0yLjUgNi01LjV6Ii8+PHBhdGggZD0iTTE4IDEzLjVsMi41IDIuNW0wIDBsMi41IDIuNW0tMi41LTIuNWwtMi41IDIuNW0yLjUtMi41bDIuNS0yLjVtLTcgMGwxLjUtMS41TTggOGwxLjUtMS41Ii8+PC9zdmc+`, // A variant of codegenie icon for settings
    SYSTEM_INFO: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiAxN2gxLjVhMy41IDMuNSAwIDAgMCAwLTdoLTVhMy41IDMuNSAwIDAgMCAwIDdoMVoiLz48Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSIxIi8+PHBhdGggZD0iTTEwIDVoNGwxIDJoLTR6Ii8+PHJlY3QgeD0iMyIgeT0iMyIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIvPjwvc3ZnPg==`,
    STORAGE_SETTINGS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0yIDhoMjBMMjIgNCAyIDR6TTIgOGgxOHYxMkgyeiIvPjxwYXRoIGQ9Ik02IDEydjRoNHYtNG01IDBoNHYtNGgtNHptLTUtNGg0djRoLTR6Ii8+PC9zdmc+`,
    WALLPAPER_HILLS_THUMB: `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAUACADASIAAhEBAxEB/8QAFwABAQEBAAAAAAAAAAAAAAAAAAIDAf/EACEQAAIBAwUBAQEAAAAAAAAAAAECAwAEERIFITETQVFh/8QAFgEBAQEAAAAAAAAAAAAAAAAAAgMB/8QAGBEBAQEBAQAAAAAAAAAAAAAAABEBEiD/2gAMAwEAAhEDEQA/ANbS2UUK8IIwTuxyTn3NJvC7K6qVDKRggggg0pChYljt1NKkCqABgAYAFALgAAADAGABS0iigCnZJFLIu7qFGce+KOigChooooAooooP/Z`, // Actual tiny jpeg of hills
    WALLPAPER_WINIA_ABSTRACT_THUMB: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDI0IDE4Ij48cmVjdCB3aWR0aD0iMjQiIGhlaWdodD0iMTgiIGZpbGw9IiNjMGMwYzAiLz48cGF0aCBkPSJNMCAwIEwxMiA5IEwyNCAwIEwxMiAxOCBaIiBmaWxsPSIjMDAwMDgwIi8+PHBhdGggZD0iTTEyIDBMMjQgOSBMMTIgMTggTDAgOSBaIiBmaWxsPSIjMDA4MDgwIiBvcGFjaXR5PSIwLjciLz48L3N2Zz4=`,
    WALLPAPER_WINIA_TEAL_THUMB: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIxOCIgdmlld0JveD0iMCAwIDI0IDE4Ij48cmVjdCB3aWR0aD0iMjQiIGhlaWdodD0iMTgiIGZpbGw9IiMwMDgwODAiLz48L3N2Zz4=`,
    WEB_VOYAGER: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIi8+PGxpbmUgeDE9IjIiIHkxPSIxMiIgeDI9IjIyIiB5Mj0iMTIiLz48cGF0aCBkPSJNMTIgMkMxNiA5IDE2IDE1IDEyIDIyTTggMTBDOCAzIDE2IDMgMTYgMTAiLz48cGF0aCBkPSJNMTYgMTRDMTYgMjEgOCAyMSA4IDE0Ii8+PHRleHQgeD0iNiIgeT0iMTcuNSIgZmlsbD0iY3VycmVudENvbG9yIiBzdHJva2U9Im5vbmUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMCIgZm9udC13ZWlnaHQ9ImJvbGQiPlc8L3RleHQ+PC9zdmc+`,
    VOYAGER_HOME: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0zIDlsOS03IDkgN3YxMGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY5eiIvPjxwYXRoIGQ9Ik05IDIydjYtMGEzIDMgMCAwIDEgNiAwdi02Ii8+PC9zdmc+`,
    VOYAGER_REFRESH: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0yMyAxMlY2TDE5IDRsLTEuNSAxLjVINmEyIDIgMCAwIDAgMCA0aDEzLjVNMCAxOHYtNmw0LTQtMS41LTEuNUgxOGEyIDIgMCAwIDEgMCA0SDQuNSIvPjwvc3ZnPg==`,
    VOYAGER_BACK: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xOSA١TDcgMTJsMTIgMTIiLz48L3N2Zz4=`,
    VOYAGER_FORWARD: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik01IDEyTDcgMGwxMiAxMi0xMiAxMi0yLTJ6Ii8+PC9zdmc+`,
    VOYAGER_STOP: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCI+PHJlY3QgeD0iNCIgeT0iNCIgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiByeD0iMiIvPjxsaW5lIHgxPSI5IiB5MT0iMTUiIHgyPSIxNSIgeTE9IjkiLz48bGluZSB4MT0iMTUiIHkxPSIxNSIgeDI9IjkiIHkyPSI5Ii8+PC9zdmc+`,
    PORTRAIT_PLACEHOLDER: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDY0IDY0IiBmaWxsPSJub25lIiBzdHJva2U9IiM4MDgwODAiIHN0cm9rZS13aWR0aD0iMiI+PHJlY3QgeD0iMTYiIHk9IjgiIHdpZHRoPSIzMiIgaGVpZ2h0PSI0OCIgcng9IjQiIGZpbGw9IiNkMGQwZDAiLz48Y2lyY2xlIGN4PSIzMiIgY3k9IjI0IiByPSI4IiBmaWxsPSIjYTBhMGEwIi8+PHJlY3QgeD0iMjQiIHk9IjM2IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9IiNhMGEwYTAiLz48L3N2Zz4=`,
    WINIA_ID_CARD_APP: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjQiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxNiIgcng9IjIiIHJ5PSIyIiBmaWxsPSIjQzBDMEMwIiBzdHJva2U9ImJsYWNrIi8+PGNpcmNsZSBjeD0iNy41IiBjeT0iOS41IiByPSIyLjUiIGZpbGw9IndoaXRlIiBzdHJva2U9ImJsYWNrIi8+PGxpbmUgeDE9IjEyIiB5MT0iOCIgeDI9IjE5IiB5Mj0iOCIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIxLjUiLz48bGluZSB4MT0iMTIiIHkxPSIxMSIgeDI9IjE5IiB5Mj0iMTEiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMS41Ii8+PGxpbmUgeDE9IjciIHkxPSIxNSIgeDI9IjE5IiB5Mj0iMTUiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMS41Ii8+PC9zdmc+`,
    MOUSE_SETTINGS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiAxN2EzIDMgMCAwIDAgMy0zaC02YTQgNCAwIDAgMCA0IDNoLTNzLTEuMy0yLTMtNHMtMy0xLjctMy00LjVhMy41IDMuNSAwIDAgMSA3IDBWNmgyVjRhMiAyIDAgMCAwLTItMkg3YTIgMiAwIDAgMC0yIDJ2MTBhMiAyIDAgMCAwIDIgMmg1WiIvPjxjaXJjbGUgY3g9IjEyIiBjeT0iNCIgcj0iMSIvPjwvc3ZnPg==`,
    // New App Icons
    WINIA_WIDGETS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSI3IiBoZWlnaHQ9IjciLz48cmVjdCB4PSIxNCIgeT0iMyIgd2lkdGg9IjciIGhlaWdodD0iNyIvPjxyZWN0IHg9IjE0IiB5PSIxNCIgd2lkdGg9IjciIGhlaWdodD0iNyIvPjxyZWN0IHg9IjMiIHk9IjE0IiB3aWR0aD0iNyIgaGVpZ2h0PSI3Ii8+PC9zdmc+`,
    WINIA_DOS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjMiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxNCIgcng9IjIiIHJ5PSIyIiBmaWxsPSJibGFjayIvPjxsaW5lIHgxPSIyMiIgeTE9IjE3IiB4Mj0iMiIgeTI9IjE3Ii8+PHRleHQgeD0iNCIgeT0iMTAiIGZpbGw9ImdyZWVuIiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjQiPkM6XD48L3RleHQ+PHJlY3QgeD0iMTAiIHk9IjgiIHdpZHRoPSI2IiBoZWlnaHQ9IjIiIGZpbGw9ImdyZWVuIi8+PC9zdmc+`,
    PIXEL_PAL: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIiBmaWxsPSIjRkZEQjAwIi8+PGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjEuNSIgZmlsbD0iYmxhY2siLz48Y2lyY2xlIGN4PSIxNSIgY3k9IjkiIHI9IjEuNSIgZmlsbD0iYmxhY2siLz48cGF0aCBkPSJNOCAxNXM0IDMgOCAwIiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz48L3N2Zz4=`,
    WINIA_STYLER: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwYXRoIGQ9Ik0xMiAydjIwTTIgMTJoMjBNNyA3bDEwIDEwTTcgMTdsMTAtMTAiLz48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiBmaWxsPSIjRkY2QjM1Ii8+PC9zdmc+`,
    SOUND_REC: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjkiIHk9IjIiIHdpZHRoPSI2IiBoZWlnaHQ9IjEyIiByeD0iMyIvPjxwYXRoIGQ9Ik0xMiAxOHYzTTggMjJoOE01IDE0YTcgNyAwIDAgMCAxNCAwIi8+PGNpcmNsZSBjeD0iMTgiIGN5PSI2IiByPSIzIiBmaWxsPSJyZWQiLz48L3N2Zz4=`,
    SOUND_PLAYER: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxwb2x5Z29uIHBvaW50cz0iNSAzIDUgMjEgMTkgMTIiIGZpbGw9IiMwMDgwODAiLz48cGF0aCBkPSJNMTEgNUw2IDloLTNhMiAyIDAgMCAwLTIgMnYyYTIgMiAwIDAgMCAyIDJoM2w1IDRoMWEyIDIgMCAwIDAgMi0yVjdhMiAyIDAgMCAwLTItMnoiLz48L3N2Zz4=`,
    CODEGENIE_HUB: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIiBmaWxsPSIjN2RiNGZhIi8+PHBhdGggZD0iTTggMTJsNCA0IDgtOCIgc3Ryb2tlPSJ3aGl0ZSIgZmlsbD0ibm9uZSIvPjx0ZXh0IHg9IjEyIiB5PSI4IiBmb250LWZhbWlseT0ibW9ub3NwYWNlIiBmb250LXNpemU9IjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj57fTwvdGV4dD48L3N2Zz4=`,
    RETRO_FX_LAB: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjMiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxNCIgcng9IjIiIHJ5PSIyIiBmaWxsPSIjRkYwMEZGIi8+PGxpbmUgeDE9IjgiIHkxPSIyMSIgeDI9IjE2IiB5Mj0iMjEiLz48bGluZSB4MT0iMTIiIHkxPSIxNyIgeDI9IjEyIiB5Mj0iMjEiLz48cGF0aCBkPSJNNiA4aDEybTAtNGgxMiIgc3Ryb2tlPSJ3aGl0ZSIvPjwvc3ZnPg==`,
    WINIA_WAVES: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjIiIHk9IjQiIHdpZHRoPSIyMCIgaGVpZ2h0PSIxNiIgcng9IjIiIGZpbGw9IiM4QjQ1MTMiLz48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiBmaWxsPSIjRkZEQjAwIi8+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMyIgZmlsbD0iIzAwMDAwMCIvPjxsaW5lIHgxPSI2IiB5MT0iOCIgeDI9IjgiIHkyPSI4IiBzdHJva2U9ImdvbGQiLz48bGluZSB4MT0iNiIgeTE9IjE2IiB4Mj0iOCIgeTI9IjE2IiBzdHJva2U9ImdvbGQiLz48L3N2Zz4=`,
};

// --- Custom Cursor SVGs (Base64 encoded) ---
const CURSORS: Record<string, string> = {
    DEFAULT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDNMMTkgMTUgTDExIDE2IEw4LjUgMTMuNSBaIi8+PC9zdmc+`, // Arrow
    HAND: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik04IDNhMiAyIDAgMCAxIDItMmg0YzEuMSAwIDIgLjkgMiAybC0zIDVoMnY3SDhWOGgybC0zLTVaIi8+PC9zdmc+`, // Hand/Pointer
    TEXT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMCA0VjIwTTEyIDRIMTJNMTIgMjBIMTBabTYtMTZoLTRtNC00aC00Ii8+PC9zdmc+`, // I-Beam
    MOVE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDVIMTNMOSAxMyA1IDlWMTFMMiAxMkw1IDE1TDkgMTNMMyAxOUw3IDIwTDEzIDE0TDIwIDdMMTkgM0wxMyA5TDExIDVaIi8+PC9zdmc+`, // Move/Grab (All directions arrow)
    RESIZE_NS: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDZIMTlMMTcgMTJMMTkgMTlMMTUgMjJMMTIgMTlMOSAxNkwxMiA5WiIvPjxwYXRoIGQ9Ik0xMiAyVjIyTTkgNkwxMiAyTDE1IDZNOSAxOEwxMiAyMkwxNSAxOFoiLz48L3N2Zz4=`, // North-South Resize
    RESIZE_EW: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yIDEybDMgM2gtNmwzLTN6TTIyIDEybC0zIDNoNmwtMy0zeiIvPjxwYXRoIGQ9Ik0yIDEySDIyTTYgOWwyIDEybDIgMTUgNmw2IDlsMiAxOGwtNi0zem0wIDBsNiAzLTE4IDNsMTggM3oiLz48L3N2Zz4=`, // East-West Resize
    RESIZE_NWSE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDRsMyAzSDNWM0g0djF6TTIwIDIwbC0zLTNIMjFWMjFIMjB2LTF6Ii8+PHBhdGggZD0iTTQgNEwyMCAyME03IDNsMy0zIDMtMyAzIDMtMyAzek0xNyAyMWwtMyAzLTEzLTEzIDMtMyAyLjUgMi41eiIvPjwvc3ZnPg==`, // Northwest-Southeast Resize
    RESIZE_NESW: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yMCA0bC0zIDNIMjFWM0gyMHYxek00IDIwbDMtM0gzVjIxSDN2LTF6Ii8+PHBhdGggZD0iTTIwIDRMNCAyME0xNyAzbDMtMy0zLTMtMyAzIDMgM3pNNyAyMWwtMyAzIDEzLTEzIDMtMy0yLjUgMi41eiIvPjwvc3ZnPg==`, // Northeast-Southwest Resize
    WAIT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik02IDJoMTJsLTYgNloiLz48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik02IDIyaDEybC02LTZaIi8+PHBhdGggZD0iTTYgMnYyLjVMOC41IDhsLTIuNSA0LjVWMTZMMTIgMjBsLTYtNC41VjEzbDIuNS00LjUtMi41LTQuNVYyeiIvPjwvc3ZnPg==`, // Wait/Hourglass
    // Specific resize handles (used by CSS classes directly, ensure these match the general ones if overridden)
    RESIZE_N: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDZIMTlMMTcgMTJMMTkgMTlMMTUgMjJMMTIgMTlMOSAxNkwxMiA5WiIvPjxwYXRoIGQ9Ik0xMiAyVjIyTTkgNkwxMiAyTDE1IDZNOSAxOEwxMiAyMkwxNSAxOFoiLz48L3N2Zz4=`,
    RESIZE_S: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0xMiAyTDE1IDZIMTlMMTcgMTJMMTkgMTlMMTUgMjJMMTIgMTlMOSAxNkwxMiA5WiIvPjxwYXRoIGQ9Ik0xMiAyVjIyTTkgNkwxMiAyTDE1IDZNOSAxOEwxMiAyMkwxNSAxOFoiLz48L3N2Zz4=`,
    RESIZE_E: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yIDEybDMgM2gtNmwzLTN6TTIyIDEybC0zIDNoNmwtMy0zeiIvPjxwYXRoIGQ9Ik0yIDEySDIyTTYgOWwyIDEybDIgMTUgNmw2IDlsMiAxOGwtNi0zem0wIDBsNiAzLTE4IDNsMTggM3oiLz48L3N2Zz4=`,
    RESIZE_W: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yIDEybDMgM2gtNmwzLTN6TTIyIDEybC0zIDNoNmwtMy0zeiIvPjxwYXRoIGQ9Ik0yIDEySDIyTTYgOWwyIDEybDIgMTUgNmw2IDlsMiAxOGwtNi0zem0wIDBsNiAzLTE4IDNsMTggM3oiLz48L3N2Zz4=`,
    RESIZE_NE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yMCA0bC0zIDNIMjFWM0gyMHYxek00IDIwbDMtM0gzVjIxSDN2LTF6Ii8+PHBhdGggZD0iTTIwIDRMNCAyME0xNyAzbDMtMy0zLTMtMyAzIDMgM3pNNyAyMWwtMyAzIDEzLTEzIDMtMy0yLjUgMi41eiIvPjwvc3ZnPg==`,
    RESIZE_SW: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik0yMCA0bC0zIDNIMjFWM0gyMHYxek00IDIwbDMtM0gzVjIxSDN2LTF6Ii8+PHBhdGggZD0iTTIwIDRMNCAyME0xNyAzbDMtMy0zLTMtMyAzIDMgM3pNNyAyMWwtMyAzIDEzLTEzIDMtMy0yLjUgMi41eiIvPjwvc3ZnPg==`,
    RESIZE_NW: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDRsMyAzSDNWM0g0djF6TTIwIDIwbC0zLTNIMjFWMjFIMjB2LTF6Ii8+PHBhdGggZD0iTTQgNEwyMCAyME03IDNsMy0zIDMtMyAzIDMtMyAzek0xNyAyMWwtMyAzLTEzLTEzIDMtMyAyLjUgMi41eiIvPjwvc3ZnPg==`,
    RESIZE_SE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJibGFjayIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIGQ9Ik00IDRsMyAzSDNWM0g0djF6TTIwIDIwbC0zLTNIMjFWMjFIMjB2LTF6Ii8+PHBhdGggZD0iTTQgNEwyMCAyME03IDNsMy0zIDMtMyAzIDMtMyAzek0xNyAyMWwtMyAzLTEzLTEzIDMtMyAyLjUgMi41eiIvPjwvc3ZnPg==`,
};

const CURSOR_HOTSPOTS: Record<string, { x: number, y: number }> = {
    DEFAULT: { x: 0, y: 0 },
    HAND: { x: 8, y: 0 },
    TEXT: { x: 8, y: 12 },
    MOVE: { x: 12, y: 12 },
    RESIZE_NS: { x: 12, y: 12 },
    RESIZE_EW: { x: 12, y: 12 },
    RESIZE_NWSE: { x: 12, y: 12 },
    RESIZE_NESW: { x: 12, y: 12 },
    WAIT: { x: 12, y: 12 },
    RESIZE_N: { x: 12, y: 12 },
    RESIZE_S: { x: 12, y: 12 },
    RESIZE_E: { x: 12, y: 12 },
    RESIZE_W: { x: 12, y: 12 },
    RESIZE_NE: { x: 12, y: 12 },
    RESIZE_SW: { x: 12, y: 12 },
    RESIZE_NW: { x: 12, y: 12 },
    RESIZE_SE: { x: 12, y: 12 },
};

const CURSOR_CLASS_MAP: Record<string, string> = {
    DEFAULT: 'cursor-default',
    HAND: 'cursor-hand',
    TEXT: 'cursor-text',
    MOVE: 'cursor-move',
    // General resize cursors for window elements
    RESIZE_NS: 'cursor-resize-ns',
    RESIZE_EW: 'cursor-resize-ew',
    RESIZE_NWSE: 'cursor-resize-nwse',
    RESIZE_NESW: 'cursor-resize-nesw',
    WAIT: 'cursor-wait',
    // Specific resize cursors for handles and body classes
    RESIZE_N: 'cursor-resize-n',
    RESIZE_S: 'cursor-resize-s',
    RESIZE_E: 'cursor-resize-e',
    RESIZE_W: 'cursor-resize-w',
    RESIZE_NE: 'cursor-resize-ne',
    RESIZE_SW: 'cursor-resize-sw',
    RESIZE_NW: 'cursor-resize-nw',
    RESIZE_SE: 'cursor-resize-se',
};


// PLACEHOLDER AVATAR SPRITES - REPLACE WITH YOUR IMAGEN-GENERATED BASE64 PNGs
const CODEGENIE_AVATAR_SPRITES: Record<string, string> = { // Explicitly typing the record
    IDLE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxlbGxpcHNlIGN4PSI4LjUiIGN5PSIxMCIgcng9IjEuNSIgcnk9IjIuNSIgZmlsbD0iYmxhY2siLz48ZWxsaXBzZSBjeD0iMTUuNSIgY3k9IjEwIiByeD0iMS41IiByeT0iMi41IiBmaWxsPSJibGFjayIvPjwvc3ZnPg==`, // Simple circle with eyes
    LISTENING_USER_PROMPT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxlbGxpcHNlIGN4PSI4LjUiIGN5PSIxMCIgcng9IjEuNSIgcnk9IjIuNSIgZmlsbD0iYmxhY2siLz48ZWxsaXBzZSBjeD0iMTUuNSIgY3k9IjEwIiByeD0iMS41IiByeT0iMi41IiBmaWxsPSJibGFjayIvPjxwYXRoIGQ9Ik0xOCAxNEMxOCAxMi45IDE3LjEgMTIgMTYgMTJDMTQuOSAxMiAxNCAxMi45IDE0IDE0SDE4WiIgZmlsbD0iYmxhY2siLz48L3N2Zz4=`, // Eyes + small mouth/attentive look
    ASKING_CLARIFICATION: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxlbGxpcHNlIGN4PSI4LjUiIGN5PSIxMCIgcng9IjEuNSIgcnk9IjIuNSIgZmlsbD0iYmxhY2siLz48ZWxsaXBzZSBjeD0iMTUuNSIgY3k9IjEwIiByeD0iMS41IiByeT0iMi41IiBmaWxsPSJibGFjayIvPjx0ZXh0IHg9IjExIiB5PSIxNyIgZm9udC1mYW1pbHk9InNlcmlmIiBmb250LXNpemU9IjUiIGZpbGw9ImJsYWNrIj4/PC90ZXh0Pjwvc3ZnPg==`, // Question mark
    THINKING_CLARIFICATION: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik04IDhMMTAgNkwxMiA4TDE0IDZMMTYgOEwxNCAxMEwxNiAxMkwxNCAxNEwxMiAxMkwxMCAxNEw4IDEyWjg4IiBmaWxsPSJibGFjayIgb3BhY2l0eT0iMC41Ii8+PC9zdmc+`, // Gear/Cog
    THINKING_APP_DESIGN: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik04IDhMMTAgNkwxMiA4TDE0IDZMMTYgOEwxNCAxMEwxNiAxMkwxNCAxNEwxMiAxMkwxMCAxNEw4IDEyWjg4IiBmaWxsPSJibGFjayIgb3BhY2l0eT0iMC41Ii8+PC9zdmc+`, // Gear/Cog
    WORKING_CODE: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik04IDEwTDExIDEzTDggMTZMNiAxMy41TDggMTBaTTE2IDEwTDEzIDEzTDE2IDE2TDE4IDEzLjVMMTYgMTBaIiBmaWxsPSJibGFjayIvPjwvc3ZnPg==`, // Tools/Pencil
    WORKING_ICON: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik04IDEwTDExIDEzTDggMTZMNiAxMy41TDggMTBaTTE2IDEwTDEzIDEzTDE2IDE2TDE4IDEzLjVMMTYgMTBaIiBmaWxsPSJibGFjayIvPjwvc3ZnPg==`, // Tools/Pencil
    SHOWING_RESULT: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik0xMiA2TDEzLjUgOS41TDE3IDEwTDE0LjUgMTIuNUwxNS41IDE2TDEyIDE0TOC41IDE2TDkuNSAxMi41TDcgMTBMMTAuNSAzLjVMMTIgNloiIGZpbGw9InllbGxvdyIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+`, // Star
    SHOWING_ERROR: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCIgdmlld0JveD0iMCAwIDI0IDI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iIzdkYjRmYSIvPjxwYXRoIGQ9Ik04IDhMMTYgMTZNOSAxNkwxNiA5Wk0xNiA4TDggMTZNOSAxNkw4IDlaIiBzdHJva2U9InJlZCIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+`, // Red X
};

interface WiniaWindow {
    id: string;
    element: HTMLElement;
    title: string;
    appId: string;
    isMinimized: boolean;
    isMaximized: boolean;
    originalRect?: { top: string, left: string, width: string, height: string };
    zIndex: number;
    taskbarButton: HTMLElement | null;
    icon: string;
    // For Control Panel navigation
    currentView?: string;
    viewHistory?: string[];
    // For Web Voyager
    voyagerIframe?: HTMLIFrameElement;
    voyagerAddressInput?: HTMLInputElement;
    voyagerLoadingIndicator?: HTMLElement;
    voyagerErrorDisplay?: HTMLElement;
    voyagerStopButton?: HTMLButtonElement;
    voyagerStatusBar?: HTMLDivElement; // For text content
    voyagerMenuBar?: HTMLElement; // The whole menu bar
    voyagerToolbarElement?: HTMLElement; // The toolbar div for show/hide
    voyagerStatusBarElement?: HTMLDivElement; // The status bar div for show/hide
    voyagerBookmarks?: { name: string, url: string }[];
    voyagerHistory?: string[]; // Simple URL history
    isOffline?: boolean; // For Web Voyager's "Work Offline"
}

interface GeneratedApp {
    id: string;
    title: string;
    htmlContent: string; // For codegenie apps
    contentGenerator?: (win: WiniaWindow, appData: GeneratedApp) => HTMLElement; // Added appData for context
    iconDataUrl: string;
    isPreinstalled?: boolean;
}

// --- File System Interfaces and Constants ---
interface FSNode {
    path: string;       // Full absolute path, e.g., "/C:/My Documents/file.txt"
    name: string;       // "file.txt"
    type: 'file' | 'folder';
    content?: string;   // For text files
    createdAt: number;  // Timestamp
    modifiedAt: number; // Timestamp
    id?: string;        // Unique ID for recycle bin items (generated upon deletion)
    originalPath?: string; // Original path before moving to recycle bin
}

const FS_STORE_NAME = "winiaFsNodes";
const RECYCLE_BIN_STORE_NAME = "winiaRecycleBinNodes";
const CUSTOM_WALLPAPERS_STORE_NAME = "winiaCustomWallpapers";
const WINIA_SETTINGS_STORE_NAME = "winiaSettings"; // For general settings
const CUSTOM_CODEGENIE_SPRITES_STORE_NAME = "winiaCustomCodeGenieSprites";
const WINIA_CUSTOM_CURSORS_STORE_NAME = "winiaCustomCursors";


// Settings Keys
const SETTING_CURRENT_WALLPAPER = "currentWallpaper"; // Can be a key for predefined or 'custom'
const SETTING_CUSTOM_WALLPAPER_ID = "customWallpaperId"; // ID if currentWallpaper is 'custom'
const SETTING_SCREENSAVER_TYPE = "screensaverType";
const SETTING_SCREENSAVER_TEXT = "screensaverText";
const SETTING_SHOW_CODEGENIE_AVATAR = "showCodeGenieAvatar";
const SETTING_SYSTEM_SOUNDS_ENABLED = "systemSoundsEnabled";
const SETTING_AMBIENT_MUSIC_ENABLED = "ambientMusicEnabled";
const SETTING_MASTER_VOLUME = "masterVolume";
const SETTING_SESSION_INITIALIZED_FLAG = "sessionInitializedFlag";
const SETTING_IMPORTED_ID_CARD_DATA = "importedIdCardData";


// Winia ID Card and System State Data Structures
interface WiniaIdCardData {
    winiaIdVersion: "1.0";
    firstName: string;
    lastName: string;
    alias?: string;
    salutationGender: 'male' | 'female' | 'neutral';
    cardColor: string; // hex
    portraitDescription: string;
    portraitDataUrl?: string; // base64
}

interface DesktopIconState {
    appId: string;
    x: string;
    y: string;
}

// OpenWindowState is deferred for a later phase
// interface OpenWindowState { ... }

interface WiniaSystemState {
    winiaSystemStateVersion: "1.0";
    winiaIdCard: WiniaIdCardData;
    winiaFsNodes: FSNode[];
    generatedApps: GeneratedApp[];
    winiaSettings: Record<string, any>;
    desktopIconsLayout: DesktopIconState[];
    // openWindowsState: OpenWindowState[]; // Deferred
}


const openWindows = new Map<string, WiniaWindow>();
let nextWindowId = 0;
let highestZIndex = 100;
let activeWindowId: string | null = null;
let windowCreationOffset = 0;

const DESKTOP_ELEMENT = document.getElementById('winia-desktop')!;
const TASKBAR_ELEMENT = document.getElementById('winia-taskbar')!;
const TASKBAR_APP_BUTTONS_ELEMENT = document.getElementById('winia-taskbar-app-buttons')!;
const START_MENU_ELEMENT = document.getElementById('winia-start-menu')!;
const START_BUTTON_ELEMENT = document.getElementById('winia-start-button')!;
const START_MENU_APPS_LIST_ELEMENT = document.getElementById('winia-start-menu-apps-list')!;
const SCREENSAVER_OVERLAY_ELEMENT = document.getElementById('winia-screensaver-overlay')!;
const IMPORT_OVERLAY_ELEMENT = document.getElementById('winia-import-overlay')!;


// CodeGenie Floating Assistant Elements
const CODEGENIE_CHARACTER_HOST_ELEMENT = document.getElementById('codegenie-character-host') as HTMLDivElement;
const CODEGENIE_AVATAR_IMG_ELEMENT = document.getElementById('codegenie-avatar-img') as HTMLImageElement;
const CODEGENIE_SPEECH_BUBBLE_ELEMENT = document.getElementById('codegenie-speech-bubble') as HTMLDivElement;
const CODEGENIE_SPEECH_BUBBLE_TEXT_ELEMENT = document.getElementById('codegenie-speech-bubble-text') as HTMLDivElement;
const CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT = document.getElementById('codegenie-speech-bubble-input') as HTMLInputElement;
const CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT = document.getElementById('codegenie-speech-bubble-actions') as HTMLDivElement;
const CODEGENIE_TRAY_ICON_ELEMENT = document.getElementById('winia-codegenie-tray-icon') as HTMLImageElement;
const CODEGENIE_PROGRESS_CONTAINER_ELEMENT = document.getElementById('codegenie-progress-container') as HTMLDivElement;
const CODEGENIE_PROGRESS_MESSAGE_ELEMENT = document.getElementById('codegenie-progress-message') as HTMLDivElement;
const CODEGENIE_PROGRESS_BAR_INNER_ELEMENT = document.getElementById('codegenie-progress-bar-inner') as HTMLDivElement;


// --- Gemini AI Initialization ---
// Corrected import for GoogleGenAI
import { GoogleGenAI as RealGoogleGenAI } from "@google/genai";
const API_KEY = process.env.API_KEY;
// Corrected initialization
let ai: RealGoogleGenAI | null = null;

if (API_KEY) {
    // Corrected initialization with named parameter
    ai = new RealGoogleGenAI({ apiKey: API_KEY });
} else {
    console.warn("API_KEY environment variable not set. CodeGenie features will be disabled.");
}

// --- IndexedDB Setup ---
const DB_NAME = "WiniaDB";
const DB_VERSION = 5; // Incremented for new custom cursors store
const APPS_STORE_NAME = "generatedApps";
let db: IDBDatabase | null = null;

async function initDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
        if (db) {
            resolve(db);
            return;
        }
        const request = indexedDB.open(DB_NAME, DB_VERSION);
        request.onupgradeneeded = (event) => {
            const tempDb = (event.target as IDBOpenDBRequest).result;
            if (!tempDb.objectStoreNames.contains(APPS_STORE_NAME)) {
                tempDb.createObjectStore(APPS_STORE_NAME, { keyPath: "id" });
            }
            if (!tempDb.objectStoreNames.contains(FS_STORE_NAME)) {
                tempDb.createObjectStore(FS_STORE_NAME, { keyPath: "path" });
            }
            if (!tempDb.objectStoreNames.contains(RECYCLE_BIN_STORE_NAME)) {
                tempDb.createObjectStore(RECYCLE_BIN_STORE_NAME, { keyPath: "id" });
            }
            if (!tempDb.objectStoreNames.contains(CUSTOM_WALLPAPERS_STORE_NAME)) {
                tempDb.createObjectStore(CUSTOM_WALLPAPERS_STORE_NAME, { keyPath: "id" });
            }
            if (!tempDb.objectStoreNames.contains(WINIA_SETTINGS_STORE_NAME)) {
                 tempDb.createObjectStore(WINIA_SETTINGS_STORE_NAME, { keyPath: "key" });
            }
            if (!tempDb.objectStoreNames.contains(CUSTOM_CODEGENIE_SPRITES_STORE_NAME)) {
                tempDb.createObjectStore(CUSTOM_CODEGENIE_SPRITES_STORE_NAME, { keyPath: "stateName" });
            }
            if (!tempDb.objectStoreNames.contains(WINIA_CUSTOM_CURSORS_STORE_NAME)) {
                tempDb.createObjectStore(WINIA_CUSTOM_CURSORS_STORE_NAME, { keyPath: "cursorName" });
            }
            console.log("IndexedDB upgrade complete or stores already exist.");
        };
        request.onsuccess = (event) => {
            db = (event.target as IDBOpenDBRequest).result;
            console.log("WiniaDB initialized successfully.");
            resolve(db);
        };
        request.onerror = (event) => {
            console.error("IndexedDB error:", (event.target as IDBOpenDBRequest).error);
            reject((event.target as IDBOpenDBRequest).error);
        };
    });
}

// --- Winia Settings Store Functions ---
async function getWiniaSetting(key: string): Promise<any | null> {
    if (!db) { // Allow checking settings even if DB isn't fully ready for other ops
        const tempDb = await initDB().catch(() => null);
        if (!tempDb) return null; // If DB fails to init here, assume setting not found
    }
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_SETTINGS_STORE_NAME, "readonly");
        const store = transaction.objectStore(WINIA_SETTINGS_STORE_NAME);
        const request = store.get(key);
        request.onsuccess = () => resolve(request.result ? request.result.value : null);
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function setWiniaSetting(key: string, value: any): Promise<void> {
    if (!db) throw new Error("DB not initialized for settings.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_SETTINGS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(WINIA_SETTINGS_STORE_NAME);
        const request = store.put({ key, value });
        request.onsuccess = () => resolve();
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function deleteWiniaSetting(key: string): Promise<void> {
    if (!db) throw new Error("DB not initialized for settings.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_SETTINGS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(WINIA_SETTINGS_STORE_NAME);
        const request = store.delete(key);
        request.onsuccess = () => resolve();
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getAllWiniaSettingsFromDB(): Promise<Record<string, any>> {
    if (!db) throw new Error("DB not initialized for settings.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_SETTINGS_STORE_NAME, "readonly");
        const store = transaction.objectStore(WINIA_SETTINGS_STORE_NAME);
        const request = store.getAll();
        const settings: Record<string, any> = {};
        request.onsuccess = () => {
            (request.result as {key: string, value: any}[]).forEach(item => {
                settings[item.key] = item.value;
            });
            resolve(settings);
        };
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}


async function saveGeneratedApp(appData: GeneratedApp): Promise<void> {
    if (!db) throw new Error("DB not initialized");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(APPS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(APPS_STORE_NAME);
        const request = store.put(appData);
        request.onsuccess = () => resolve();
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getGeneratedApp(appId: string): Promise<GeneratedApp | null> {
    if (!db) throw new Error("DB not initialized");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(APPS_STORE_NAME, "readonly");
        const store = transaction.objectStore(APPS_STORE_NAME);
        const request = store.get(appId);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getAllGeneratedApps(): Promise<GeneratedApp[]> {
    if (!db) throw new Error("DB not initialized");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(APPS_STORE_NAME, "readonly");
        const store = transaction.objectStore(APPS_STORE_NAME);
        const request = store.getAll();
        request.onsuccess = () => {
            console.log("getAllGeneratedApps result:", request.result);
            resolve(request.result || []);
        };
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}


// --- Clock ---
function updateClock() {
    const clockElement = document.getElementById('winia-clock');
    if (clockElement) {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        clockElement.textContent = `${hours}:${minutes}`;
    }
}

// --- Sound Engine ---
let audioCtx: AudioContext | null = null;
let masterGainNode: GainNode | null = null;
let systemSoundsEnabled = true;
let ambientMusicEnabled = false;
let winiaMasterVolume = 0.7; // Default volume (0.0 to 1.0)

function initAudio(): boolean {
    if (!audioCtx && typeof window !== 'undefined' && (window.AudioContext || (window as any).webkitAudioContext)) {
        try {
            audioCtx = new (window.AudioContext || (window as any).webkitAudioContext)();
            masterGainNode = audioCtx.createGain();
            masterGainNode.gain.value = winiaMasterVolume;
            masterGainNode.connect(audioCtx.destination);
            return true;
        } catch (e) {
            console.warn("Web Audio API is not supported or failed to initialize.", e);
            return false;
        }
    }
    return !!audioCtx; // Already initialized or still not initializable
}


const SOUND_EFFECTS: Record<string, (ctx: AudioContext, gainNode: GainNode) => void> = {
    click: (ctx, gain) => {
        const osc = ctx.createOscillator();
        const env = ctx.createGain();
        osc.type = 'triangle';
        osc.frequency.setValueAtTime(880, ctx.currentTime); // A5
        env.gain.setValueAtTime(0.3, ctx.currentTime);
        env.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.05);
        osc.connect(env);
        env.connect(gain);
        osc.start(ctx.currentTime);
        osc.stop(ctx.currentTime + 0.05);
    },
    error: (ctx, gain) => {
        const osc1 = ctx.createOscillator();
        const osc2 = ctx.createOscillator();
        const env = ctx.createGain();
        osc1.type = 'square';
        osc1.frequency.setValueAtTime(330, ctx.currentTime); // E4
        osc2.type = 'square';
        osc2.frequency.setValueAtTime(220, ctx.currentTime); // A3
        env.gain.setValueAtTime(0.2, ctx.currentTime);
        env.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.15);
        osc1.connect(env);
        osc2.connect(env);
        env.connect(gain);
        osc1.start(ctx.currentTime);
        osc1.stop(ctx.currentTime + 0.15);
        osc2.start(ctx.currentTime);
        osc2.stop(ctx.currentTime + 0.15);
    },
    window_action: (ctx, gain) => { // Generic for open/close/min/max
        const osc = ctx.createOscillator();
        const env = ctx.createGain();
        osc.type = 'sine';
        osc.frequency.setValueAtTime(660, ctx.currentTime);
        env.gain.setValueAtTime(0.15, ctx.currentTime);
        env.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.08);
        osc.connect(env);
        env.connect(gain);
        osc.start(ctx.currentTime);
        osc.stop(ctx.currentTime + 0.08);
    },
    file_delete: () => console.log("[SOUND LOG] Playing: file_delete_to_recycle_bin (not implemented with Web Audio yet)"),
    empty_recycle_bin: () => console.log("[SOUND LOG] Playing: empty_recycle_bin_sound (not implemented with Web Audio yet)"),
    voyager_navigate: (ctx, gain) => { // Sound for browser navigation
        const osc = ctx.createOscillator();
        const env = ctx.createGain();
        osc.type = 'sawtooth';
        osc.frequency.setValueAtTime(440, ctx.currentTime);
        osc.frequency.linearRampToValueAtTime(880, ctx.currentTime + 0.05);
        env.gain.setValueAtTime(0.1, ctx.currentTime);
        env.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.1);
        osc.connect(env);
        env.connect(gain);
        osc.start(ctx.currentTime);
        osc.stop(ctx.currentTime + 0.1);
    },
    id_card_portrait_generate: (ctx, gain) => {
        const osc = ctx.createOscillator();
        const noise = ctx.createBufferSource();
        const noiseBuffer = ctx.createBuffer(1, ctx.sampleRate * 0.3, ctx.sampleRate);
        const output = noiseBuffer.getChannelData(0);
        for (let i = 0; i < noiseBuffer.length; i++) { output[i] = Math.random() * 0.2 - 0.1; }
        noise.buffer = noiseBuffer;

        const env = ctx.createGain();
        osc.type = 'sine';
        osc.frequency.setValueAtTime(500, ctx.currentTime);
        osc.frequency.exponentialRampToValueAtTime(1200, ctx.currentTime + 0.2);

        env.gain.setValueAtTime(0.15, ctx.currentTime);
        env.gain.exponentialRampToValueAtTime(0.0001, ctx.currentTime + 0.3);
        osc.connect(env);
        noise.connect(env);
        env.connect(gain);
        osc.start(ctx.currentTime);
        osc.stop(ctx.currentTime + 0.3);
        noise.start(ctx.currentTime);
        noise.stop(ctx.currentTime + 0.3);
    }
};

function playSound(soundName: keyof typeof SOUND_EFFECTS) {
    if (!systemSoundsEnabled) {
        console.log(`[SOUND] Suppressed (system sounds disabled): ${soundName}`);
        return;
    }
    if (!initAudio() || !audioCtx || !masterGainNode) { // Ensure initAudio() is called
        console.warn(`Audio context not available for sound: ${soundName}. System sounds enabled: ${systemSoundsEnabled}`);
        return;
    }


    const soundPlayer = SOUND_EFFECTS[soundName];
    if (typeof soundPlayer === 'function') {
        if (soundPlayer.length === 2) { // Check if it expects ctx and gain
             soundPlayer(audioCtx, masterGainNode);
        } else {
            (soundPlayer as () => void)(); // Assume it's a simple function
        }
    } else {
        console.warn(`Sound effect '${soundName}' not found or not playable.`);
    }
}

function updateMasterVolume(volume: number) {
    winiaMasterVolume = Math.max(0, Math.min(1, volume)); // Clamp between 0 and 1
    if (masterGainNode) {
        masterGainNode.gain.setValueAtTime(winiaMasterVolume, audioCtx?.currentTime || 0);
    }
    setWiniaSetting(SETTING_MASTER_VOLUME, winiaMasterVolume);
}

// --- Window Resize State ---
let isResizing = false;
let resizeHandleType: string | null = null;
let resizeInitialX: number, resizeInitialY: number;
let resizeInitialWidth: number, resizeInitialHeight: number;
let resizeTargetWindow: WiniaWindow | null = null;
const MIN_WINDOW_WIDTH = 150;
const MIN_WINDOW_HEIGHT = 100;

// --- Window Management ---
function createWindow(
    appId: string,
    title: string,
    contentGeneratorOrHtml: ((win: WiniaWindow, appData: GeneratedApp) => HTMLElement) | string,
    icon: string = ICONS.APP_DEFAULT,
    defaultWidth: string = '450px',
    defaultHeight: string = '350px',
    appDataForGenerator?: GeneratedApp // Pass appData for contentGenerator
): WiniaWindow {
    const windowId = `winia-window-${nextWindowId++}`;

    const windowElement = document.createElement('div');
    windowElement.className = 'winia-window';
    windowElement.id = windowId;
    windowElement.style.width = defaultWidth;
    windowElement.style.height = defaultHeight;

    const cascadeOffset = (windowCreationOffset % 5) * 25;
    windowElement.style.left = `${50 + cascadeOffset}px`;
    windowElement.style.top = `${50 + cascadeOffset}px`;
    windowCreationOffset++;

    const titleBar = document.createElement('div');
    titleBar.className = 'winia-window-title-bar cursor-move';

    const windowIconImg = document.createElement('img');
    windowIconImg.src = icon;
    windowIconImg.alt = `${title} icon`;
    windowIconImg.className = 'winia-window-icon';

    const titleText = document.createElement('span');
    titleText.className = 'winia-window-title';
    titleText.textContent = title;

    const controls = document.createElement('div');
    controls.className = 'winia-window-controls';

    const minimizeButton = createControlButton('Minimize', ICONS.MINIMIZE, () => minimizeWindow(windowId));
    const maximizeButton = createControlButton('Maximize', ICONS.MAXIMIZE, () => toggleMaximizeWindow(windowId));
    const closeButton = createControlButton('Close', ICONS.CLOSE, () => closeWindow(windowId));

    controls.append(minimizeButton, maximizeButton, closeButton);
    titleBar.append(windowIconImg, titleText, controls);

    const contentArea = document.createElement('div');
    contentArea.className = 'winia-window-content';


    const newWindow: WiniaWindow = {
        id: windowId, element: windowElement, title, appId,
        isMinimized: false, isMaximized: false, zIndex: 0,
        taskbarButton: null, icon,
        voyagerBookmarks: appId === webVoyagerAppId ? [] : undefined,
        voyagerHistory: appId === webVoyagerAppId ? [] : undefined,
    };

    if (typeof contentGeneratorOrHtml === 'string') {
        contentArea.innerHTML = contentGeneratorOrHtml;
        Array.from(contentArea.getElementsByTagName('script')).forEach(oldScript => {
            const newScript = document.createElement('script');
            Array.from(oldScript.attributes).forEach(attr => newScript.setAttribute(attr.name, attr.value));
            newScript.appendChild(document.createTextNode(oldScript.innerHTML));
            oldScript.parentNode?.replaceChild(newScript, oldScript);
        });
    } else {
        contentArea.appendChild(contentGeneratorOrHtml(newWindow, appDataForGenerator!));
    }

    // Add resize handles
    const resizeHandles = ['n', 's', 'e', 'w', 'ne', 'nw', 'se', 'sw'];
    resizeHandles.forEach(type => {
        const handle = document.createElement('div');
        handle.className = `winia-resize-handle winia-resize-handle-${type} cursor-resize-${type}`;
        handle.dataset.resizeType = type;
        handle.addEventListener('mousedown', (e) => startResize(e as MouseEvent, newWindow, type));
        windowElement.appendChild(handle);
    });


    windowElement.append(titleBar, contentArea);
    DESKTOP_ELEMENT.appendChild(windowElement);

    openWindows.set(windowId, newWindow);
    makeDraggable(windowElement, titleBar);
    setActiveWindow(windowId);
    createTaskbarButton(newWindow);

    windowElement.addEventListener('mousedown', (e) => {
        playSound('click');
        setActiveWindow(windowId)
    }, true);
    playSound('window_action');
    return newWindow;
}

function startResize(event: MouseEvent, win: WiniaWindow, handleType: string) {
    if (win.isMaximized) return;
    event.preventDefault();
    event.stopPropagation();
    playSound('click');

    isResizing = true;
    resizeTargetWindow = win;
    resizeHandleType = handleType;
    resizeInitialX = event.clientX;
    resizeInitialY = event.clientY;
    const rect = win.element.getBoundingClientRect();
    const desktopRect = DESKTOP_ELEMENT.getBoundingClientRect(); // get origin for relative positioning
    resizeInitialWidth = rect.width;
    resizeInitialHeight = rect.height;
    // Store initial top/left relative to the desktop element, not viewport
    resizeTargetWindow.element.style.left = `${rect.left - desktopRect.left}px`;
    resizeTargetWindow.element.style.top = `${rect.top - desktopRect.top}px`;


    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', stopResize);
    document.body.classList.add(`cursor-resize-${handleType}`);
    DESKTOP_ELEMENT.classList.add(`cursor-resize-${handleType}`);
}

function handleResize(event: MouseEvent) {
    if (!isResizing || !resizeTargetWindow || !resizeHandleType) return;
    event.preventDefault();

    const deltaX = event.clientX - resizeInitialX;
    const deltaY = event.clientY - resizeInitialY;

    let newLeft = parseFloat(resizeTargetWindow.element.style.left!);
    let newTop = parseFloat(resizeTargetWindow.element.style.top!);
    let newWidth = resizeInitialWidth;
    let newHeight = resizeInitialHeight;

    if (resizeHandleType.includes('e')) newWidth = Math.max(MIN_WINDOW_WIDTH, resizeInitialWidth + deltaX);
    if (resizeHandleType.includes('s')) newHeight = Math.max(MIN_WINDOW_HEIGHT, resizeInitialHeight + deltaY);

    if (resizeHandleType.includes('w')) {
        newWidth = Math.max(MIN_WINDOW_WIDTH, resizeInitialWidth - deltaX);
        if (newWidth > MIN_WINDOW_WIDTH || resizeInitialWidth - deltaX > MIN_WINDOW_WIDTH ) { // only update left if width changes
            newLeft = parseFloat(resizeTargetWindow.element.style.left!) + (resizeInitialWidth - newWidth);
        }
    }
    if (resizeHandleType.includes('n')) {
        newHeight = Math.max(MIN_WINDOW_HEIGHT, resizeInitialHeight - deltaY);
        if (newHeight > MIN_WINDOW_HEIGHT || resizeInitialHeight - deltaY > MIN_WINDOW_HEIGHT) { // only update top if height changes
             newTop = parseFloat(resizeTargetWindow.element.style.top!) + (resizeInitialHeight - newHeight);
        }
    }

    // Prevent window from going off-screen (basic boundary checks)
    const desktopRect = DESKTOP_ELEMENT.getBoundingClientRect();
    if (newLeft < 0) { newWidth += newLeft; newLeft = 0; }
    if (newTop < 0) { newHeight += newTop; newTop = 0; }
    if (newLeft + newWidth > desktopRect.width) newWidth = desktopRect.width - newLeft;
    if (newTop + newHeight > desktopRect.height) newHeight = desktopRect.height - newTop;


    resizeTargetWindow.element.style.left = `${newLeft}px`;
    resizeTargetWindow.element.style.top = `${newTop}px`;
    resizeTargetWindow.element.style.width = `${Math.max(MIN_WINDOW_WIDTH, newWidth)}px`;
    resizeTargetWindow.element.style.height = `${Math.max(MIN_WINDOW_HEIGHT, newHeight)}px`;
}

function stopResize() {
    if (!isResizing) return;
    isResizing = false;
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
    if (resizeHandleType) {
        document.body.classList.remove(`cursor-resize-${resizeHandleType}`);
        DESKTOP_ELEMENT.classList.remove(`cursor-resize-${resizeHandleType}`);
    }
    resizeTargetWindow = null;
    resizeHandleType = null;
}


function createControlButton(label: string, iconSrc: string, onClick: () => void, small: boolean = false): HTMLButtonElement {
    const button = document.createElement('button');
    button.className = 'cursor-hand';
    if (small) button.classList.add('small-control-button');
    button.setAttribute('aria-label', label);
    const img = document.createElement('img');
    img.src = iconSrc;
    img.alt = label;
    if (small) {
        img.style.width = '14px';
        img.style.height = '14px';
    }
    button.appendChild(img);
    button.addEventListener('click', (e) => {
        playSound('click');
        e.stopPropagation();
        onClick();
    });
    button.addEventListener('mousedown', (e) => { e.stopPropagation(); });
    return button;
}

function setActiveWindow(windowId: string | null) {
    if (activeWindowId === windowId && windowId !== null && !openWindows.get(windowId)?.isMinimized) return;

    highestZIndex++;
    if (activeWindowId && openWindows.has(activeWindowId)) {
        const oldActive = openWindows.get(activeWindowId)!;
        oldActive.element.classList.remove('active');
        if (oldActive.taskbarButton) oldActive.taskbarButton.classList.remove('active');
    }

    activeWindowId = windowId;

    if (windowId && openWindows.has(windowId)) {
        const newActive = openWindows.get(windowId)!;
        newActive.element.classList.add('active');
        newActive.element.style.zIndex = highestZIndex.toString();
        newActive.zIndex = highestZIndex;
        if (newActive.taskbarButton) newActive.taskbarButton.classList.add('active');
        if (newActive.isMinimized) {
            newActive.element.style.display = '';
            newActive.isMinimized = false;
            playSound('window_action');
        }
         // Focus handling for specific apps like Web Voyager
        if (newActive.appId === webVoyagerAppId && newActive.voyagerAddressInput) {
            if (document.activeElement !== newActive.voyagerAddressInput) {
                 setTimeout(() => newActive.voyagerAddressInput!.focus(), 0); // Timeout to ensure visibility
            }
        }

    }
    updateTaskbarButtons();
}

function closeWindow(windowId: string) {
    const win = openWindows.get(windowId);
    if (win) {
        // Specific cleanup for Web Voyager iframe
        if (win.appId === webVoyagerAppId && win.voyagerIframe) {
            win.voyagerIframe.src = 'about:blank'; // Clear content to stop potential scripts/audio
            win.voyagerIframe.remove();
            win.voyagerIframe = undefined;
        }

        win.element.remove();
        if (win.taskbarButton) win.taskbarButton.remove();
        openWindows.delete(windowId);
        playSound('window_action');
        if (activeWindowId === windowId) {
            let nextActiveId: string | null = null;
            let maxZ = -1;
            openWindows.forEach(w => {
                if (!w.isMinimized && w.zIndex > maxZ) {
                    maxZ = w.zIndex;
                    nextActiveId = w.id;
                }
            });
            setActiveWindow(nextActiveId);
        }
    }
    removeExplorerContextMenu();
}

function minimizeWindow(windowId: string) {
    const win = openWindows.get(windowId);
    if (win && !win.isMinimized) {
        win.element.style.display = 'none';
        win.isMinimized = true;
        playSound('window_action');
        if (win.taskbarButton) win.taskbarButton.classList.remove('active');
        if (activeWindowId === windowId) {
            let nextActiveId: string | null = null;
            let maxZ = -1;
            openWindows.forEach(w => {
                if (!w.isMinimized && w.zIndex > maxZ && w.id !== windowId) {
                    maxZ = w.zIndex;
                    nextActiveId = w.id;
                }
            });
            setActiveWindow(nextActiveId);
        }
    }
    updateTaskbarButtons();
}

function toggleMaximizeWindow(windowId: string) {
    const win = openWindows.get(windowId);
    if (!win) return;

    const maximizeButton = win.element.querySelector('.winia-window-controls button[aria-label="Maximize"], .winia-window-controls button[aria-label="Restore"]') as HTMLButtonElement;
    const maximizeIcon = maximizeButton.querySelector('img');
    const resizeHandles = win.element.querySelectorAll('.winia-resize-handle');

    if (win.isMaximized) {
        if (win.originalRect) {
            win.element.style.top = win.originalRect.top;
            win.element.style.left = win.originalRect.left;
            win.element.style.width = win.originalRect.width;
            win.element.style.height = win.originalRect.height;
        }
        win.element.classList.remove('maximized');
        resizeHandles.forEach(h => (h as HTMLElement).style.display = '');
        win.isMaximized = false;
        if (maximizeButton && maximizeIcon) {
            maximizeButton.setAttribute('aria-label', 'Maximize');
            maximizeIcon.src = ICONS.MAXIMIZE;
            maximizeIcon.alt = 'Maximize';
        }
    } else {
        win.originalRect = {
            top: win.element.style.top, left: win.element.style.left,
            width: win.element.style.width, height: win.element.style.height,
        };
        const desktopRect = DESKTOP_ELEMENT.getBoundingClientRect();
        win.element.style.top = '0px';
        win.element.style.left = '0px';
        win.element.style.width = `${desktopRect.width}px`;
        win.element.style.height = `${desktopRect.height}px`;
        win.element.classList.add('maximized');
        resizeHandles.forEach(h => (h as HTMLElement).style.display = 'none');
        win.isMaximized = true;
        if (maximizeButton && maximizeIcon) {
            maximizeButton.setAttribute('aria-label', 'Restore');
            maximizeIcon.src = ICONS.RESTORE;
            maximizeIcon.alt = 'Restore';
        }
    }
    playSound('window_action');
    setActiveWindow(windowId);
}


function makeDraggable(element: HTMLElement, handle: HTMLElement) {
    let isDragging = false;
    let offsetX: number, offsetY: number;

    handle.addEventListener('mousedown', (e) => {
        if ((e.target as HTMLElement).closest('button') || isResizing) return;
        const win = openWindows.get(element.id);
        if (win && win.isMaximized) return;

        isDragging = true;
        offsetX = e.clientX - element.offsetLeft;
        offsetY = e.clientY - element.offsetTop;
        element.classList.add('cursor-grabbing');
        document.body.classList.add('cursor-grabbing-global'); // Apply to body for smoother drag feel
        setActiveWindow(element.id);
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        const desktopRect = DESKTOP_ELEMENT.getBoundingClientRect();
        let newX = e.clientX - offsetX;
        let newY = e.clientY - offsetY;

        newX = Math.max(0, Math.min(newX, desktopRect.width - element.offsetWidth));
        newY = Math.max(0, Math.min(newY, desktopRect.height - element.offsetHeight));

        element.style.left = `${newX}px`;
        element.style.top = `${newY}px`;
    });

    document.addEventListener('mouseup', () => {
        if (isDragging) {
            isDragging = false;
            element.classList.remove('cursor-grabbing');
            document.body.classList.remove('cursor-grabbing-global');
        }
    });
}

// --- Taskbar Button Management ---
function createTaskbarButton(win: WiniaWindow) {
    const button = document.createElement('button');
    button.className = 'taskbar-app-button cursor-hand';
    button.setAttribute('data-window-id', win.id);

    const iconImg = document.createElement('img');
    iconImg.src = win.icon;
    iconImg.alt = '';
    button.appendChild(iconImg);

    const titleSpan = document.createElement('span');
    titleSpan.textContent = win.title;
    button.appendChild(titleSpan);

    button.addEventListener('click', () => {
        playSound('click');
        if (win.isMinimized) setActiveWindow(win.id);
        else if (activeWindowId === win.id) minimizeWindow(win.id);
        else setActiveWindow(win.id);
    });
    TASKBAR_APP_BUTTONS_ELEMENT.appendChild(button);
    win.taskbarButton = button;
    updateTaskbarButtons();
}

function updateTaskbarButtons() {
    openWindows.forEach(win => {
        if (win.taskbarButton) {
            if (activeWindowId === win.id && !win.isMinimized) {
                win.taskbarButton.classList.add('active');
            } else {
                win.taskbarButton.classList.remove('active');
            }
        }
    });
}

// --- Start Menu ---
function toggleStartMenu() {
    playSound('click');
    const isHidden = START_MENU_ELEMENT.classList.toggle('hidden');
    START_BUTTON_ELEMENT.setAttribute('aria-expanded', isHidden ? 'false' : 'true');
    if (!isHidden) START_MENU_ELEMENT.focus();
    else START_BUTTON_ELEMENT.focus();
    removeExplorerContextMenu();
}

function addAppToStartMenu(app: GeneratedApp) {
    console.log("addAppToStartMenu called for:", app.title, app.id);
    if (START_MENU_APPS_LIST_ELEMENT.querySelector(`li[data-app-id="${app.id}"]`)) {
        console.log("Start menu item already exists for:", app.id);
        return;
    }

    const listItem = document.createElement('li');
    listItem.setAttribute('role', 'menuitem');
    listItem.setAttribute('data-app-id', app.id);
    listItem.setAttribute('data-app-title', app.title);
    listItem.tabIndex = 0;
    listItem.classList.add('cursor-hand');

    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.alt = '';
    img.className = 'start-menu-item-icon';

    listItem.append(img, document.createTextNode(` ${app.title}`));

    listItem.addEventListener('click', () => { playSound('click'); launchAppById(app.id); });
    listItem.addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') { playSound('click'); launchAppById(app.id); }
    });

    START_MENU_APPS_LIST_ELEMENT.appendChild(listItem);
}


// --- Application Launchers ---
const notepadAppId = 'notepad';
const winiaExplorerAppId = 'winiaExplorer';
const recycleBinAppId = 'recycleBin';
const controlPanelAppId = 'controlPanel';
const webVoyagerAppId = 'webVoyager';
const winiaIdCreatorAppId = 'winiaIdCreator';
const winiaWidgetsAppId = 'winiaWidgets';
const winiaDosPromptAppId = 'winiaDosPrompt';
const pixelPalAppId = 'pixelPal';
const winiaStylerAppId = 'winiaStyler';
const winiaSoundRecAppId = 'winiaSoundRec';
const winiaSoundPlayerAppId = 'winiaSoundPlayer';
const codeGenieHubAppId = 'codeGenieHub';
const retroFxLabAppId = 'retroFxLab';
const winiaWavesRadioAppId = 'winiaWavesRadio';


async function launchAppById(appId: string, launchParams?: { filePathToOpen?: string, initialExplorerPath?: string, newVoyagerUrl?: string }) {
    for (const win of openWindows.values()) {
        // If app is already open and not minimized, and not specifically trying to open a new file/path/URL, activate it.
        if (win.appId === appId && !win.isMinimized && !launchParams?.filePathToOpen && !launchParams?.initialExplorerPath && !launchParams?.newVoyagerUrl) {
            setActiveWindow(win.id);
            if (!START_MENU_ELEMENT.classList.contains('hidden')) toggleStartMenu();
            return;
        }
        // If it's minimized, just activate it (unless specific params given that might warrant a new window)
        if (win.appId === appId && win.isMinimized && !launchParams?.filePathToOpen && !launchParams?.initialExplorerPath && !launchParams?.newVoyagerUrl) {
            setActiveWindow(win.id);
            if (!START_MENU_ELEMENT.classList.contains('hidden')) toggleStartMenu();
            return;
        }
    }


    const appData = await getGeneratedApp(appId);
    if (!appData) {
        console.warn(`App data not found for ID: ${appId}`);
        playSound('error');
        if (!START_MENU_ELEMENT.classList.contains('hidden')) toggleStartMenu();
        return;
    }

    if (appData.isPreinstalled) {
        if (appId === notepadAppId) {
            launchNotepadWindow(appData, launchParams?.filePathToOpen);
        } else if (appId === winiaExplorerAppId) {
            launchWiniaExplorerWindow(appData, launchParams?.initialExplorerPath || "/C:/");
        } else if (appId === recycleBinAppId) {
            launchRecycleBinWindow(appData);
        } else if (appId === controlPanelAppId) {
            launchControlPanelWindow(appData);
        } else if (appId === webVoyagerAppId) {
            const newWin = launchWebVoyagerWindow(appData);
            if (launchParams?.newVoyagerUrl && newWin.voyagerIframe && newWin.voyagerAddressInput) {
                setTimeout(() => {
                    const voyagerElements = {
                        iframe: newWin.voyagerIframe!,
                        addressInput: newWin.voyagerAddressInput!,
                    };
                     const navigateFunc = (newWin as any)._navigateToUrlInternal;
                     if (navigateFunc) {
                        navigateFunc(launchParams.newVoyagerUrl);
                     } else if (newWin.voyagerAddressInput && newWin.voyagerIframe) {
                        newWin.voyagerAddressInput.value = launchParams.newVoyagerUrl!;
                         updateVoyagerDisplayState(newWin, `Loading ${launchParams.newVoyagerUrl}...`, 'loading');
                         newWin.voyagerIframe.src = 'about:blank';
                         setTimeout(() => {
                            if(newWin.voyagerIframe) newWin.voyagerIframe.src = launchParams.newVoyagerUrl!;
                            playSound('voyager_navigate');
                         },0);
                     }
                }, 100);
            }
        } else if (appId === winiaIdCreatorAppId) {
            launchWiniaIdCreatorWindow(appData);
        } else if (appData.contentGenerator) {
             createWindow(appData.id, appData.title, appData.contentGenerator, appData.iconDataUrl, undefined, undefined, appData);
        }
    } else if (appData.htmlContent) {
        createWindow(appData.id, appData.title, appData.htmlContent, appData.iconDataUrl);
    } else {
        console.error(`App ${appId} has no htmlContent or contentGenerator. AppData from DB:`, appData);
        playSound('error');
    }

    if (!START_MENU_ELEMENT.classList.contains('hidden')) {
        toggleStartMenu();
    }
}

function launchNotepadWindow(appData: GeneratedApp, filePathToOpen?: string) {
    let currentFilePath = filePathToOpen;
    let windowTitle = appData.title + (filePathToOpen ? ` - ${getNodeName(filePathToOpen)}` : " - Untitled");

    createWindow(appData.id, windowTitle, (win) => { // appData not strictly needed here, but passed for consistency
        const container = document.createElement('div');
        container.className = 'notepad-container';

        const menuBar = buildNotepadMenuBar(
            async () => {
                const textarea = container.querySelector('textarea')!;
                const content = textarea.value;
                if (currentFilePath) {
                    await updateFsNodeContent(currentFilePath, content);
                    console.log(`File saved: ${currentFilePath}`);
                } else {
                    const newPath = window.prompt("Save As (e.g., /C:/My Documents/file.txt):", "/C:/My Documents/newfile.txt");
                    if (newPath) {
                        try {
                            await createFsNode({ path: newPath, type: 'file', content });
                            currentFilePath = newPath;
                            win.title = `${appData.title} - ${getNodeName(newPath)}`;
                            const titleEl = win.element.querySelector('.winia-window-title');
                            if (titleEl) titleEl.textContent = win.title;
                            if (win.taskbarButton) {
                                const taskbarTitle = win.taskbarButton.querySelector('span');
                                if (taskbarTitle) taskbarTitle.textContent = win.title;
                            }
                            console.log(`File saved as: ${newPath}`);
                        } catch (e) {
                            alert(`Error saving file: ${(e as Error).message}`);
                            playSound('error');
                        }
                    }
                }
            },
            () => {
                closeWindow(win.id);
            }
        );
        container.appendChild(menuBar);

        const textarea = document.createElement('textarea');
        textarea.setAttribute('aria-label', 'Notepad text area');
        textarea.classList.add('cursor-text');
        if (filePathToOpen) {
            getFsNode(filePathToOpen).then(node => {
                if (node && node.type === 'file') textarea.value = node.content || '';
            });
        }
        container.appendChild(textarea);
        return container;
    }, appData.iconDataUrl);
}

function buildNotepadMenuBar(onSave: () => void, onExit: () => void): HTMLElement {
    const menuBar = document.createElement('div');
    menuBar.className = 'notepad-menu-bar';

    const fileButton = document.createElement('button');
    fileButton.className = 'notepad-menu-button cursor-hand';
    fileButton.textContent = 'File';

    const dropdown = document.createElement('div');
    dropdown.className = 'notepad-dropdown-menu hidden';

    const saveItem = document.createElement('div');
    saveItem.className = 'notepad-dropdown-item cursor-hand';
    saveItem.textContent = 'Save';
    saveItem.onclick = (e) => { playSound('click'); e.stopPropagation(); onSave(); dropdown.classList.add('hidden'); };

    const exitItem = document.createElement('div');
    exitItem.className = 'notepad-dropdown-item cursor-hand';
    exitItem.textContent = 'Exit';
    exitItem.onclick = (e) => { playSound('click'); e.stopPropagation(); onExit(); dropdown.classList.add('hidden');};

    dropdown.append(saveItem, exitItem);
    menuBar.appendChild(fileButton);
    menuBar.appendChild(dropdown);

    fileButton.addEventListener('click', (e) => {
        playSound('click');
        e.stopPropagation();
        dropdown.classList.toggle('hidden');
    });
    document.addEventListener('click', (e) => {
        if (!menuBar.contains(e.target as Node)) {
            dropdown.classList.add('hidden');
        }
    });
    return menuBar;
}

// --- Winia Explorer ---
function launchWiniaExplorerWindow(appData: GeneratedApp, initialPath: string) {
    let currentPath = normalizePath(initialPath);
    // Ensure explorer window has a reference to its content container for refresh
    const explorerWindow = createWindow(
        appData.id,
        `Explorer - ${getNodeName(currentPath)}`, // Initial title
        (win) => {
            const ui = buildExplorerUI(currentPath, win.id);
            // Store the container reference in the window object for easier access later
            (win as any).explorerContainer = ui;
            return ui;
        },
        currentPath === '/C:/' ? ICONS.MY_COMPUTER : ICONS.FOLDER_OPEN, // Initial icon
        '550px', '400px'
    );
    explorerWindow.element.dataset.currentExplorerPath = currentPath;
}


function buildExplorerUI(initialPath: string, winiaWindowId: string): HTMLElement {
    const container = document.createElement('div');
    container.className = 'explorer-container';
    container.dataset.winiaWindowId = winiaWindowId;


    const toolbar = document.createElement('div');
    toolbar.className = 'explorer-toolbar';
    const upButton = createControlButton('Up', ICONS.ARROW_UP, () => {
        // Fix: Cast container.closest result to HTMLElement | null to access dataset
        const closestWindowElement = container.closest('.winia-window') as HTMLElement | null;
        const currentExplorerPath = (closestWindowElement?.dataset.currentExplorerPath) || container.dataset.currentPath!;
        const parentPath = getParentPath(currentExplorerPath);
        if (parentPath) {
            refreshExplorerView(winiaWindowId, parentPath, container);
        }
    });
    upButton.disabled = normalizePath(initialPath) === '/C:/';
    toolbar.appendChild(upButton);

    const addressBar = document.createElement('div');
    addressBar.className = 'explorer-address-bar';
    const addressLabel = document.createElement('span');
    addressLabel.textContent = 'Address: ';
    const addressInput = document.createElement('input');
    addressInput.type = 'text';
    addressInput.className = 'explorer-address-input cursor-text';
    addressInput.readOnly = true;
    addressInput.value = initialPath;
    addressBar.append(addressLabel, addressInput);

    const itemsView = document.createElement('div');
    itemsView.className = 'explorer-items-view';

    container.append(toolbar, addressBar, itemsView);
    container.dataset.currentPath = initialPath; // Set initial path on the container itself

    renderExplorerItems(itemsView, initialPath, winiaWindowId, container);
    return container;
}

async function renderExplorerItems(itemsViewElement: HTMLElement, path: string, winiaWindowId: string, explorerContainer: HTMLElement) {
    itemsViewElement.innerHTML = '';
    explorerContainer.dataset.currentPath = path; // Update current path on the container

    const addressInput = explorerContainer.querySelector('.explorer-address-input') as HTMLInputElement;
    if (addressInput) addressInput.value = path;

    const upButton = explorerContainer.querySelector('.explorer-toolbar button[aria-label="Up"]') as HTMLButtonElement;
    if (upButton) upButton.disabled = normalizePath(path) === '/C:/';

    const winInstance = openWindows.get(winiaWindowId);
    if (winInstance) {
        winInstance.element.dataset.currentExplorerPath = path; // Also update on window element for consistency
        const newTitle = `Explorer - ${getNodeName(path)}`;
        winInstance.title = newTitle;
        const titleEl = winInstance.element.querySelector('.winia-window-title');
        if(titleEl) titleEl.textContent = newTitle;
        const iconEl = winInstance.element.querySelector('.winia-window-icon') as HTMLImageElement;
        if(iconEl) iconEl.src = path === '/C:/' ? ICONS.MY_COMPUTER : ICONS.FOLDER_OPEN;
        if (winInstance.taskbarButton) {
            const taskbarTitle = winInstance.taskbarButton.querySelector('span');
            if (taskbarTitle) taskbarTitle.textContent = newTitle;
            const taskbarIcon = winInstance.taskbarButton.querySelector('img');
            if(taskbarIcon) taskbarIcon.src = path === '/C:/' ? ICONS.MY_COMPUTER : ICONS.FOLDER_OPEN;
        }
    }

    try {
        const items = await listDirectoryContents(path);
        items.sort((a, b) => {
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;
            return a.name.localeCompare(b.name);
        });

        if (items.length === 0) {
            itemsViewElement.textContent = 'This folder is empty.';
            itemsViewElement.style.textAlign = 'center';
            itemsViewElement.style.paddingTop = '20px';
        } else {
            itemsViewElement.style.textAlign = 'left';
            itemsViewElement.style.paddingTop = '0';
        }

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'explorer-item cursor-hand';
            itemElement.setAttribute('data-path', item.path);
            itemElement.tabIndex = 0;

            const iconImg = document.createElement('img');
            iconImg.src = item.type === 'folder' ? ICONS.FOLDER_CLOSED : (item.name.endsWith('.txt') ? ICONS.FILE_TEXT : ICONS.APP_DEFAULT);
            iconImg.alt = item.type;

            const nameSpan = document.createElement('span');
            nameSpan.textContent = item.name;

            itemElement.append(iconImg, nameSpan);
            itemElement.addEventListener('dblclick', () => {
                playSound('click');
                if (item.type === 'folder') {
                    refreshExplorerView(winiaWindowId, item.path, explorerContainer);
                } else if (item.name.endsWith('.txt')) {
                    launchAppById(notepadAppId, { filePathToOpen: item.path });
                }
            });
            itemElement.addEventListener('keydown', async (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                    playSound('click');
                     if (item.type === 'folder') {
                        refreshExplorerView(winiaWindowId, item.path, explorerContainer);
                    } else if (item.name.endsWith('.txt')) {
                        launchAppById(notepadAppId, { filePathToOpen: item.path });
                    }
                } else if (e.key === 'Delete' && document.activeElement === itemElement) {
                    if (window.confirm(`Are you sure you want to send '${item.name}' to the Recycle Bin?`)) {
                        await handleExplorerItemDelete(item, winiaWindowId, explorerContainer);
                    }
                }
            });
            itemElement.addEventListener('contextmenu', (e) => {
                playSound('click');
                e.preventDefault();
                createExplorerContextMenu(item, e.clientX, e.clientY, winiaWindowId, explorerContainer);
            });
            itemElement.addEventListener('focus', () => itemElement.classList.add('selected'));
            itemElement.addEventListener('blur', () => itemElement.classList.remove('selected'));

            itemsViewElement.appendChild(itemElement);
        });
    } catch (error) {
        console.error("Error rendering explorer items:", error);
        itemsViewElement.textContent = `Error loading: ${(error as Error).message}`;
        playSound('error');
    }
}

async function handleExplorerItemDelete(fsNode: FSNode, winiaWindowId: string, explorerContainer: HTMLElement) {
    try {
        const success = await deleteFsNodeLogic(fsNode.path, true);
        if (success) {
            playSound('file_delete');
            const currentExplorerPath = explorerContainer.dataset.currentPath;
            if (currentExplorerPath) {
                refreshExplorerView(winiaWindowId, currentExplorerPath, explorerContainer);
            }
        } else {
            alert(`Could not delete '${fsNode.name}'. It might be in use or no longer exist.`);
            playSound('error');
        }
    } catch (error) {
        console.error("Error during handleExplorerItemDelete for:", fsNode.path, error);
        alert(`Error deleting '${fsNode.name}': ${(error as Error).message}`);
        playSound('error');
    }
}

function removeExplorerContextMenu() {
    const existingMenu = document.querySelector('.explorer-context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }
}

function createExplorerContextMenu(fsNode: FSNode, x: number, y: number, winiaWindowId: string, explorerContainer: HTMLElement) {
    removeExplorerContextMenu();


    const menu = document.createElement('div');
    menu.className = 'explorer-context-menu';
    menu.style.left = `${x}px`;
    menu.style.top = `${y}px`;

    const closeMenu = () => {
        menu.remove();
        document.removeEventListener('click', handleOutsideClickContextMenu, true);
        window.removeEventListener('keydown', handleEscapeKeyContextMenu);
    };

    const handleOutsideClickContextMenu = (event: MouseEvent) => {
        if (!menu.contains(event.target as Node)) {
            closeMenu();
        }
    };
    const handleEscapeKeyContextMenu = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            closeMenu();
        }
    };

    const openItem = document.createElement('div');
    openItem.className = 'explorer-context-menu-item cursor-hand';
    openItem.textContent = 'Open';
    openItem.onclick = () => {
        playSound('click');
        if (fsNode.type === 'folder') {
            refreshExplorerView(winiaWindowId, fsNode.path, explorerContainer);
        } else if (fsNode.name.endsWith('.txt')) {
            launchAppById(notepadAppId, { filePathToOpen: fsNode.path });
        }
        closeMenu();
    };
    menu.appendChild(openItem);

    const deleteItem = document.createElement('div');
    deleteItem.className = 'explorer-context-menu-item cursor-hand';
    deleteItem.textContent = 'Delete';
    deleteItem.onclick = async () => {
        playSound('click');
        if (window.confirm(`Are you sure you want to send '${fsNode.name}' to the Recycle Bin?`)) {
            await handleExplorerItemDelete(fsNode, winiaWindowId, explorerContainer);
        }
        closeMenu();
    };
    menu.appendChild(deleteItem);

    document.body.appendChild(menu);

    setTimeout(() => {
        document.addEventListener('click', handleOutsideClickContextMenu, true);
        window.addEventListener('keydown', handleEscapeKeyContextMenu);
    }, 0);
}


function refreshExplorerView(winiaWindowId: string, newPath: string, explorerContainer: HTMLElement) {
    const itemsView = explorerContainer.querySelector('.explorer-items-view') as HTMLElement;
    if (!itemsView) {
        // Fallback if explorerContainer is not the direct parent of itemsView (e.g. if it's the window content div)
        const win = openWindows.get(winiaWindowId);
        const actualContainer = win?.element.querySelector('.explorer-container');
        const actualItemsView = actualContainer?.querySelector('.explorer-items-view') as HTMLElement;
        if(actualItemsView && actualContainer) {
            renderExplorerItems(actualItemsView, newPath, winiaWindowId, actualContainer as HTMLElement);
        } else {
            console.error("Items view not found in explorer container for refresh:", explorerContainer);
        }
        return;
    }
    renderExplorerItems(itemsView, newPath, winiaWindowId, explorerContainer);
}


// --- CodeGenie Floating Assistant ---
// Enum keys are used as DB keys for custom sprites, ensure they are string-friendly
enum CodeGenieAvatarState {
    IDLE = "IDLE",
    LISTENING_USER_PROMPT = "LISTENING_USER_PROMPT",
    THINKING_CLARIFICATION = "THINKING_CLARIFICATION",
    ASKING_CLARIFICATION = "ASKING_CLARIFICATION",
    THINKING_APP_DESIGN = "THINKING_APP_DESIGN",
    WORKING_CODE = "WORKING_CODE",
    WORKING_ICON = "WORKING_ICON",
    SHOWING_RESULT = "SHOWING_RESULT",
    SHOWING_ERROR = "SHOWING_ERROR",
}


let currentCodeGenieState = CodeGenieAvatarState.IDLE;
let isCodeGenieAssistantActive = false;
let originalUserPrompt = "";
let clarificationQuestion = "";
let activeCodeGenieInteractionToken: symbol | null = null;
let codegenieProgressInterval: number | null = null;
let codegenieMessageInterval: number | null = null;

const CODEGENIE_PROGRESS_MESSAGES = [
    "Consultando los planos de la creatividad...",
    "Mezclando código con nostalgia...",
    "Alineando los píxeles perfectos...",
    "Depurando con rayos catódicos...",
    "Invocando espíritus del silicio...",
    "Optimizando para la velocidad de un 486...",
    "Comprobando si hay suficiente RAM (simulada)...",
    "Dibujando iconos con precisión de ratón de bola...",
    "Compilando... quiero decir, ¡creando magia!",
    "Esto tomará solo un momento cósmico...",
    "Ajustando los condensadores de fluzo...",
    "Pintando los bytes con colores retro...",
    "¡Casi listo! Engrasando los bits finales...",
];

function delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function updateCodeGenieAvatar(state: CodeGenieAvatarState) {
    currentCodeGenieState = state;
    const stateName = CodeGenieAvatarState[state] as keyof typeof CodeGenieAvatarState; // Get string name from enum

    try {
        const customSprite = await getCustomCodeGenieSprite(stateName);
        if (customSprite?.dataUrl) {
            CODEGENIE_AVATAR_IMG_ELEMENT.src = customSprite.dataUrl;
        } else {
            CODEGENIE_AVATAR_IMG_ELEMENT.src = CODEGENIE_AVATAR_SPRITES[stateName] || CODEGENIE_AVATAR_SPRITES.IDLE;
        }
    } catch (error) {
        console.error(`Error loading sprite for state ${stateName}:`, error);
        CODEGENIE_AVATAR_IMG_ELEMENT.src = CODEGENIE_AVATAR_SPRITES[stateName] || CODEGENIE_AVATAR_SPRITES.IDLE;
    }
}


function showSpeechBubble(
    text: string | null,
    showInput: boolean,
    actions: { label: string, onClick: () => void }[] = [],
    showProgress: boolean = false,
    initialProgressMessage?: string
) {
    CODEGENIE_SPEECH_BUBBLE_TEXT_ELEMENT.textContent = text;
    CODEGENIE_SPEECH_BUBBLE_TEXT_ELEMENT.classList.toggle('hidden', !text || showProgress);

    CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.classList.toggle('hidden', !showInput || showProgress);
    if (showInput) CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.value = '';

    CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT.innerHTML = '';
    if (actions.length > 0 && !showProgress) {
        CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT.classList.remove('hidden');
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'cursor-hand';
            button.textContent = action.label;
            button.onclick = (e) => {
                playSound('click');
                e.stopPropagation();
                action.onClick();
            };
            CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT.appendChild(button);
        });
    } else {
        CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT.classList.add('hidden');
    }

    CODEGENIE_PROGRESS_CONTAINER_ELEMENT.classList.toggle('hidden', !showProgress);
    if (showProgress) {
        CODEGENIE_PROGRESS_MESSAGE_ELEMENT.textContent = initialProgressMessage || "Procesando...";
        CODEGENIE_PROGRESS_BAR_INNER_ELEMENT.style.width = '0%';
    }

    CODEGENIE_SPEECH_BUBBLE_ELEMENT.classList.remove('hidden');
    if (showInput && !showProgress) CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.focus();
}

function hideSpeechBubble() {
    CODEGENIE_SPEECH_BUBBLE_ELEMENT.classList.add('hidden');
    stopCodeGenieProgressAnimation();
}

function stopCodeGenieProgressAnimation() {
    if (codegenieProgressInterval) {
        clearInterval(codegenieProgressInterval);
        codegenieProgressInterval = null;
    }
    if (codegenieMessageInterval) {
        clearInterval(codegenieMessageInterval);
        codegenieMessageInterval = null;
    }
    CODEGENIE_PROGRESS_BAR_INNER_ELEMENT.style.width = '0%';
}

function startCodeGenieProgressAnimation(durationMs: number, initialMessage: string): Promise<void> {
    return new Promise((resolve) => {
        stopCodeGenieProgressAnimation();

        let currentProgress = 0;
        const progressIncrement = 100 / (durationMs / 100);
        let messageIdx = 0;

        CODEGENIE_PROGRESS_MESSAGE_ELEMENT.textContent = initialMessage;
        CODEGENIE_PROGRESS_BAR_INNER_ELEMENT.style.width = '0%';

        codegenieProgressInterval = window.setInterval(() => {
            currentProgress += progressIncrement;
            if (currentProgress >= 100) {
                CODEGENIE_PROGRESS_BAR_INNER_ELEMENT.style.width = '100%';
                stopCodeGenieProgressAnimation();
                resolve();
            } else {
                CODEGENIE_PROGRESS_BAR_INNER_ELEMENT.style.width = `${currentProgress}%`;
            }
        }, 100);

        codegenieMessageInterval = window.setInterval(() => {
            messageIdx = (messageIdx + 1) % CODEGENIE_PROGRESS_MESSAGES.length;
            CODEGENIE_PROGRESS_MESSAGE_ELEMENT.textContent = CODEGENIE_PROGRESS_MESSAGES[messageIdx];
        }, 1500);
    });
}


function dismissCodeGenieAssistant() {
    activeCodeGenieInteractionToken = null;
    hideSpeechBubble();
    updateCodeGenieAvatar(CodeGenieAvatarState.IDLE);
    getWiniaSetting(SETTING_SHOW_CODEGENIE_AVATAR).then(showAvatar => {
        if (showAvatar === false) {
            CODEGENIE_CHARACTER_HOST_ELEMENT.classList.add('hidden');
        }
    });
    isCodeGenieAssistantActive = false;
    DESKTOP_ELEMENT.classList.remove('cursor-wait');
    document.body.classList.remove('cursor-wait-global');
    console.log("CodeGenie dismissed and interaction token invalidated.");
}

async function handleCodeGenieInteraction(expectedToken: symbol | null, userResponse?: string | boolean) {
    if (activeCodeGenieInteractionToken !== expectedToken) {
        console.log("CodeGenie interaction aborted: token mismatch or superseded.");
        DESKTOP_ELEMENT.classList.remove('cursor-wait');
        document.body.classList.remove('cursor-wait-global');
        return;
    }

    DESKTOP_ELEMENT.classList.add('cursor-wait');
    document.body.classList.add('cursor-wait-global');

    if (!ai) {
        showSpeechBubble("CodeGenie is offline. API Key might be missing.", false, [{ label: "Dismiss", onClick: dismissCodeGenieAssistant }]);
        updateCodeGenieAvatar(CodeGenieAvatarState.SHOWING_ERROR);
        playSound('error');
        DESKTOP_ELEMENT.classList.remove('cursor-wait');
        document.body.classList.remove('cursor-wait-global');
        return;
    }

    try {
        switch (currentCodeGenieState) {
            case CodeGenieAvatarState.LISTENING_USER_PROMPT:
                originalUserPrompt = CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.value.trim();
                if (!originalUserPrompt) {
                    showSpeechBubble("Please tell me what app you'd like to create!", true, [{ label: "Send", onClick: () => handleCodeGenieInteraction(expectedToken) }]);
                    DESKTOP_ELEMENT.classList.remove('cursor-wait');
                    document.body.classList.remove('cursor-wait-global');
                    return;
                }
                updateCodeGenieAvatar(CodeGenieAvatarState.THINKING_CLARIFICATION);
                showSpeechBubble("Let me think... 🤔", false);

                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }
                await delay(1000);
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }

                const clarificationPrompt = `You are CodeGenie, a friendly and helpful AI assistant. A user wants to create an app: "${originalUserPrompt}".
To make it even better, can you think of one simple "Yes/No" question? This could be to clarify something, or a fun, creative suggestion for their app. Make your question sound natural and helpful.
If you have a great question, just state the question.
If you think their idea is clear enough or you don't have a suggestion right now, just say "NO_QUESTION".`;
                const clarificationResponse: GenerateContentResponse = await ai.models.generateContent({
                    model: 'gemini-2.5-flash-preview-04-17',
                    contents: clarificationPrompt
                });
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }
                clarificationQuestion = clarificationResponse.text.trim();

                if (clarificationQuestion && clarificationQuestion.toUpperCase() !== "NO_QUESTION" && clarificationQuestion !== "") {
                    updateCodeGenieAvatar(CodeGenieAvatarState.ASKING_CLARIFICATION);
                    showSpeechBubble(clarificationQuestion, false, [
                        { label: "Yes", onClick: () => handleCodeGenieInteraction(expectedToken, true) },
                        { label: "No", onClick: () => handleCodeGenieInteraction(expectedToken, false) }
                    ]);
                } else {
                    clarificationQuestion = "";
                    updateCodeGenieAvatar(CodeGenieAvatarState.THINKING_APP_DESIGN);
                    showSpeechBubble("Okay, got it! Designing your app now... 🎨", false);
                    if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }
                    await delay(1000);
                    if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }
                    await handleCodeGenieInteraction(expectedToken);
                }
                break;

            case CodeGenieAvatarState.ASKING_CLARIFICATION:
            case CodeGenieAvatarState.THINKING_APP_DESIGN:
                let combinedPrompt = originalUserPrompt;
                if (clarificationQuestion && typeof userResponse === 'boolean') {
                    combinedPrompt += ` (User answered "${userResponse ? 'Yes' : 'No'}" to: ${clarificationQuestion})`;
                }

                updateCodeGenieAvatar(CodeGenieAvatarState.WORKING_CODE);
                const codeGenDuration = 3000 + Math.random() * 2000;
                showSpeechBubble(null, false, [], true, "Alright, conjuring up some code... 🧑‍💻");
                const codeGenPromise = startCodeGenieProgressAnimation(codeGenDuration, "Alright, conjuring up some code... 🧑‍💻");
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }

                const appTitle = combinedPrompt.substring(0, 25).replace(/[<>&"]/g, "") + (combinedPrompt.length > 25 ? "..." : "");
                const appCode = await generateAppCode(combinedPrompt, appTitle);
                await codeGenPromise;
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }

                if (!appCode) {
                    updateCodeGenieAvatar(CodeGenieAvatarState.SHOWING_ERROR);
                    showSpeechBubble("I hit a snag trying to write the code. Maybe try a different idea?", false, [
                        { label: "Try Again", onClick: () => toggleCodeGenieAssistantInteraction() },
                        { label: "Dismiss", onClick: dismissCodeGenieAssistant }
                    ]);
                    playSound('error');
                    DESKTOP_ELEMENT.classList.remove('cursor-wait');
                    document.body.classList.remove('cursor-wait-global');
                    return;
                }

                updateCodeGenieAvatar(CodeGenieAvatarState.WORKING_ICON);
                const iconGenDuration = 2000 + Math.random() * 1500;
                showSpeechBubble(null, false, [], true, "Polishing it off with a cool icon... ✨");
                const iconGenPromise = startCodeGenieProgressAnimation(iconGenDuration, "Polishing it off with a cool icon... ✨");
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }

                const iconDataUrl = await generateAppIcon(appTitle);
                await iconGenPromise;
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }

                const newApp: GeneratedApp = {
                    id: `codegenie-app-${Date.now()}`,
                    title: appTitle,
                    htmlContent: appCode,
                    iconDataUrl,
                };

                await saveGeneratedApp(newApp);
                if (activeCodeGenieInteractionToken !== expectedToken) { DESKTOP_ELEMENT.classList.remove('cursor-wait'); document.body.classList.remove('cursor-wait-global'); return; }
                createDesktopIcon(newApp);
                addAppToStartMenu(newApp);

                updateCodeGenieAvatar(CodeGenieAvatarState.SHOWING_RESULT);
                showSpeechBubble(`Your app "${appTitle}" is ready!`, false, [
                    { label: "Launch it!", onClick: () => { launchAppById(newApp.id); dismissCodeGenieAssistant(); } },
                    { label: "Cool!", onClick: dismissCodeGenieAssistant }
                ]);
                break;

            default:
                console.warn("CodeGenie in unexpected state:", currentCodeGenieState);
                dismissCodeGenieAssistant();
        }
    } catch (error) {
        if (activeCodeGenieInteractionToken === expectedToken) {
            console.error("CodeGenie Interaction Error:", error);
            updateCodeGenieAvatar(CodeGenieAvatarState.SHOWING_ERROR);
            showSpeechBubble(`Oh dear, something went wrong: ${(error as Error).message}. Maybe try again?`, false, [
                { label: "Try Again", onClick: toggleCodeGenieAssistantInteraction },
                { label: "Dismiss", onClick: dismissCodeGenieAssistant }
            ]);
            playSound('error');
        } else {
            console.error("CodeGenie Interaction Error (stale interaction):", error);
        }
    } finally {
        if (currentCodeGenieState !== CodeGenieAvatarState.WORKING_CODE && currentCodeGenieState !== CodeGenieAvatarState.WORKING_ICON) {
           stopCodeGenieProgressAnimation();
        }
        // Always remove wait cursor unless still in a progress state
        if (!CODEGENIE_PROGRESS_CONTAINER_ELEMENT || CODEGENIE_PROGRESS_CONTAINER_ELEMENT.classList.contains('hidden')) {
             DESKTOP_ELEMENT.classList.remove('cursor-wait');
             document.body.classList.remove('cursor-wait-global');
        }
    }
}


async function generateAppCode(userPrompt: string, appTitle: string): Promise<string | null> {
    if (!ai) return null;
    const safeAppTitleId = appTitle.replace(/[^a-zA-Z0-9-_]/g, '-').toLowerCase();
    const fullPrompt = `You are CodeGenie, creating a mini-app for a retro OS.
User wants: "${userPrompt}". App title: "${appTitle}".
**CRITICAL INSTRUCTIONS:**
1.  **Retro Theme:** "Windows Classic" (95/98/2000) aesthetic. Fonts: Tahoma, "MS Sans Serif", Arial. Colors: Grays (#C0C0C0 bg, #FFFFFF content), black text. UI Elements: Classic style (e.g., 'outset' borders for buttons). NO modern styles unless retro-themed. Input fields should use .cursor-text class. Buttons should use .cursor-hand class.
2.  **Full Container Fill:** Root HTML element(s) MUST fill 100% width/height of parent \`div.winia-window-content\`. DO NOT add outer borders/margins to YOUR app's root. Ensure it has a background matching the OS theme like #C0C0C0.
3.  **Self-Contained:** All CSS in \`<style>\`, all JS in \`<script>\`. JS self-executing/auto-initializing. No external libraries.
4.  **Structure:** Output ONLY the HTML for the app (divs, etc.). NO \`<html>\`, \`<head>\`, \`<body>\`.
5.  **CSS Specificity:** Use unique IDs/classes (e.g., prefixed with "${safeAppTitleId}-app-") to avoid conflicts.
**Example "Retro Counter":**
<div id="${safeAppTitleId}-app-container" style="width:100%;height:100%;background-color:#C0C0C0;padding:10px;box-sizing:border-box;display:flex;flex-direction:column;align-items:center;justify-content:center;font-family:Tahoma,sans-serif;">
<style>
#${safeAppTitleId}-app-container h3{margin-top:0;margin-bottom:10px;color:#000;font-size:14px;}
#${safeAppTitleId}-app-container .counter-display{font-size:24px;margin-bottom:15px;padding:5px 10px;border:2px inset #808080;background-color:#FFF;min-width:60px;text-align:center;font-family:"MS Sans Serif",Arial,sans-serif;}
#${safeAppTitleId}-app-container .buttons-container{display:flex;gap:10px;}
#${safeAppTitleId}-app-container button{font-family:Tahoma,sans-serif;font-size:12px;background-color:#C0C0C0;color:#000;border:2px outset #FFF;padding:5px 10px;min-width:40px;}
#${safeAppTitleId}-app-container button:active{border-style:inset;padding:6px 9px 4px 11px;}
/* Cursor classes from main app - assume they are available */
.cursor-hand { cursor: url('${CURSORS.HAND}') 8 0, pointer; }
.cursor-text { cursor: url('${CURSORS.TEXT}') 8 12, text; }
</style>
<h3>${appTitle}</h3>
<div class="counter-display" id="${safeAppTitleId}-app-count">0</div>
<div class="buttons-container">
<button id="${safeAppTitleId}-app-decrement" aria-label="Decrement" class="cursor-hand">-</button>
<button id="${safeAppTitleId}-app-increment" aria-label="Increment" class="cursor-hand">+</button>
</div>
<script>
(()=>{const c=document.getElementById("${safeAppTitleId}-app-count"),i=document.getElementById("${safeAppTitleId}-app-increment"),d=document.getElementById("${safeAppTitleId}-app-decrement");let t=0;function u(){c&&(c.textContent=t)}i&&i.addEventListener("click",()=>{t++;u()});d&&d.addEventListener("click",()=>{t--;u()});u()})();
<\/script></div>
Provide only the HTML, CSS, and JS. Do not wrap in markdown.`;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: 'gemini-2.5-flash-preview-04-17',
            contents: fullPrompt
        });
        let code = response.text;
        if (!code) return null;
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = code.match(fenceRegex);
        if (match && match[2]) code = match[2].trim();
        return code;
    } catch (e) { console.error("App code gen error:", e); playSound('error'); return null; }
}

async function generateAppIcon(appName: string): Promise<string> {
    if (!ai) return ICONS.APP_DEFAULT;
    try {
        const prompt = `Pixel art icon for a '${appName}' app, 32x32 pixels, absolutely transparent background (no background color, no matte), PNG format. Classic retro OS style. Clear subject representing app type. No border/frame on icon itself.`;
        const response = await ai.models.generateImages({
            model: 'imagen-3.0-generate-002',
            prompt,
            config: { numberOfImages: 1, outputMimeType: 'image/png' },
        });
        if (response.generatedImages?.[0]?.image?.imageBytes) {
            return `data:image/png;base64,${response.generatedImages[0].image.imageBytes}`;
        }
        return ICONS.APP_DEFAULT;
    } catch (e) { console.error("App icon gen error:", e); playSound('error'); return ICONS.APP_DEFAULT; }
}

async function toggleCodeGenieAssistantInteraction() {
    playSound('click');
    if (!ai && !isCodeGenieAssistantActive) {
        alert("CodeGenie Assistant is not available: API Key not configured.");
        playSound('error');
        return;
    }

    const showAvatar = await getWiniaSetting(SETTING_SHOW_CODEGENIE_AVATAR);

    if (isCodeGenieAssistantActive) {
        dismissCodeGenieAssistant();
    } else {
        isCodeGenieAssistantActive = true;
        activeCodeGenieInteractionToken = Symbol("codegenie_interaction_token");
        const currentToken = activeCodeGenieInteractionToken;

        CODEGENIE_CHARACTER_HOST_ELEMENT.classList.remove('hidden');
        updateCodeGenieAvatar(CodeGenieAvatarState.LISTENING_USER_PROMPT);
        originalUserPrompt = "";
        clarificationQuestion = "";
        showSpeechBubble("Hi there! What app shall we create today?", true, [
            { label: "Send", onClick: () => handleCodeGenieInteraction(currentToken) }
        ]);
        setTimeout(() => CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.focus(), 100);
    }
}

// --- Helper: Fisher-Yates Shuffle ---
function shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// --- Desktop Icon & App Loading ---
function createDesktopIcon(app: GeneratedApp, x?: string, y?: string) {
    console.log("createDesktopIcon called for:", app.title, app.id);
    if (document.querySelector(`.desktop-icon[data-app-id="${app.id}"]`)) {
        console.log("Desktop icon already exists for:", app.id);
        return;
    }

    const iconElement = document.createElement('div');
    iconElement.className = 'desktop-icon cursor-hand';
    iconElement.setAttribute('data-app-id', app.id);
    iconElement.tabIndex = 0;
    iconElement.setAttribute('role', 'button');
    iconElement.setAttribute('aria-label', `Open ${app.title}`);
    iconElement.style.position = 'relative'; // For pixel overlay positioning

    const img = document.createElement('img');
    img.src = app.iconDataUrl;
    img.alt = '';

    const span = document.createElement('span');
    span.textContent = app.title;

    // Set initial state - make icons visible immediately
    img.style.opacity = '1';
    span.style.opacity = '1';
    img.classList.add('desktop-icon-content-fade-in'); // CSS class for transition
    span.classList.add('desktop-icon-content-fade-in');

    iconElement.append(img, span);

    if (x !== undefined && y !== undefined) {
        iconElement.style.position = 'absolute'; // This overrides the relative above for placed icons
        iconElement.style.left = x;
        iconElement.style.top = y;
    } else {
         iconElement.style.position = 'relative'; // Ensure it's relative for flex items if no x/y
    }


    iconElement.addEventListener('dblclick', () => { playSound('click'); launchAppById(app.id); });
    iconElement.addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') { playSound('click'); launchAppById(app.id); }
    });

    iconElement.addEventListener('focus', () => iconElement.classList.add('selected'));
    iconElement.addEventListener('blur', () => iconElement.classList.remove('selected'));

    DESKTOP_ELEMENT.appendChild(iconElement);

    // --- Pixel Dissolve Animation ---
    // Use a slight delay to ensure image dimensions are available
    requestAnimationFrame(() => {
        const imgElementForEffect = iconElement.querySelector('img');
        if (!imgElementForEffect) return;

        const effectWidth = imgElementForEffect.offsetWidth || 32;
        const effectHeight = imgElementForEffect.offsetHeight || 32;
        const overlayTop = imgElementForEffect.offsetTop;
        const overlayLeft = imgElementForEffect.offsetLeft;


        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = `${overlayTop}px`;
        overlay.style.left = `${overlayLeft}px`;
        overlay.style.width = `${effectWidth}px`;
        overlay.style.height = `${effectHeight}px`;
        overlay.style.zIndex = '1'; // Above image, below potential other elements
        iconElement.appendChild(overlay);

        const pixelSize = 4;
        const numCols = Math.ceil(effectWidth / pixelSize);
        const numRows = Math.ceil(effectHeight / pixelSize);
        const pixelBlocks: HTMLElement[] = [];
        const desktopBgColor = getComputedStyle(document.documentElement).getPropertyValue('--winia-desktop-bg').trim();


        for (let r = 0; r < numRows; r++) {
            for (let c = 0; c < numCols; c++) {
                const pixelBlock = document.createElement('div');
                pixelBlock.style.position = 'absolute';
                pixelBlock.style.left = `${c * pixelSize}px`;
                pixelBlock.style.top = `${r * pixelSize}px`;
                pixelBlock.style.width = `${pixelSize}px`;
                pixelBlock.style.height = `${pixelSize}px`;
                pixelBlock.style.backgroundColor = desktopBgColor || '#008080';
                pixelBlock.style.opacity = '1';
                // Individual pixel fade uses ease-in-out, overall effect achieved by staggering
                pixelBlock.style.transition = 'opacity 0.7s cubic-bezier(0.42, 0, 0.58, 1)';
                overlay.appendChild(pixelBlock);
                pixelBlocks.push(pixelBlock);
            }
        }

        shuffleArray(pixelBlocks);

        const totalAnimationDuration = 1500; // ms
        const individualPixelFadeOutDuration = 700; // ms, matches transition above

        // Stagger the start of pixel fades over the first part of the total duration
        const staggerWindow = totalAnimationDuration - individualPixelFadeOutDuration;

        pixelBlocks.forEach((pBlock, index) => {
            // Distribute delays, apply ease-in-out to distribution via Math.pow
            const progress = index / pixelBlocks.length;
            const easedProgress = Math.pow(progress, 2); // Ease-in like behavior for starting fades
            const delay = easedProgress * staggerWindow;

            setTimeout(() => {
                pBlock.style.opacity = '0';
            }, delay);
        });

        // Icons are already visible, no need to fade them in again
        // requestAnimationFrame(() => {
        //     img.style.opacity = '1';
        //     span.style.opacity = '1';
        // });

        // Remove overlay after the entire animation duration
        setTimeout(() => {
            overlay.remove();
        }, totalAnimationDuration);
    });
}

async function ensurePreinstalledAppsInDB() {
    console.log("Starting ensurePreinstalledAppsInDB...");
    const appsToEnsure: GeneratedApp[] = [
        {
            id: notepadAppId, title: 'Notepad', htmlContent: '',
            iconDataUrl: ICONS.NOTEPAD, isPreinstalled: true,
            contentGenerator: (win) => buildEnhancedNotepadUI(win)
        },
        {
            id: winiaExplorerAppId, title: 'Winia Explorer', htmlContent: '',
            iconDataUrl: ICONS.MY_COMPUTER, isPreinstalled: true,
            contentGenerator: (win) => buildExplorerUI("/C:/", win.id)
        },
        {
            id: recycleBinAppId, title: 'Recycle Bin', htmlContent: '',
            iconDataUrl: ICONS.RECYCLE_BIN_EMPTY, isPreinstalled: true,
            contentGenerator: (win) => buildRecycleBinUI(win.id)
        },
        {
            id: controlPanelAppId, title: 'Control Panel', htmlContent: '',
            iconDataUrl: ICONS.CONTROL_PANEL, isPreinstalled: true,
            contentGenerator: (win) => buildControlPanelMainUI(win)
        },
        {
            id: webVoyagerAppId, title: 'Web Voyager', htmlContent: '',
            iconDataUrl: ICONS.WEB_VOYAGER, isPreinstalled: true,
            contentGenerator: (win, appData) => createEnhancedWebVoyagerContent(win, appData)
        },
        {
            id: winiaIdCreatorAppId, title: 'Winia ID Card Creator', htmlContent: '',
            iconDataUrl: ICONS.WINIA_ID_CARD_APP, isPreinstalled: true,
            contentGenerator: (win, appData) => buildWiniaIdCreatorUI(win, appData)
        },
        {
            id: winiaWidgetsAppId, title: 'WiniaWidgets', htmlContent: '',
            iconDataUrl: ICONS.WINIA_WIDGETS, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaWidgetsUI(win)
        },
        {
            id: winiaDosPromptAppId, title: 'WiniaDOS Prompt', htmlContent: '',
            iconDataUrl: ICONS.WINIA_DOS, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaDosPromptUI(win)
        },
        {
            id: pixelPalAppId, title: 'PixelPal', htmlContent: '',
            iconDataUrl: ICONS.PIXEL_PAL, isPreinstalled: true,
            contentGenerator: (win) => buildPixelPalUI(win)
        },
        {
            id: winiaStylerAppId, title: 'WiniaStyler Engine', htmlContent: '',
            iconDataUrl: ICONS.WINIA_STYLER, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaStylerUI(win)
        },
        {
            id: winiaSoundRecAppId, title: 'WiniaSoundRec', htmlContent: '',
            iconDataUrl: ICONS.SOUND_REC, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaSoundRecUI(win)
        },
        {
            id: winiaSoundPlayerAppId, title: 'WiniaSound Player', htmlContent: '',
            iconDataUrl: ICONS.SOUND_PLAYER, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaSoundPlayerUI(win)
        },
        {
            id: codeGenieHubAppId, title: 'CodeGenie App Hub', htmlContent: '',
            iconDataUrl: ICONS.CODEGENIE_HUB, isPreinstalled: true,
            contentGenerator: (win) => buildCodeGenieHubUI(win)
        },
        {
            id: retroFxLabAppId, title: 'RetroFX Lab', htmlContent: '',
            iconDataUrl: ICONS.RETRO_FX_LAB, isPreinstalled: true,
            contentGenerator: (win) => buildRetroFxLabUI(win)
        },
        {
            id: winiaWavesRadioAppId, title: 'Winia Waves Radio Player', htmlContent: '',
            iconDataUrl: ICONS.WINIA_WAVES, isPreinstalled: true,
            contentGenerator: (win) => buildWiniaWavesRadioUI(win)
        }
    ];

    console.log("Apps to ensure:", appsToEnsure.length);

    for (const app of appsToEnsure) {
        try {
            console.log(`Checking app: ${app.title} (${app.id})`);
            const existingApp = await getGeneratedApp(app.id);
            if (!existingApp) {
                console.log(`Saving new app: ${app.title}`);
                await saveGeneratedApp({ ...app, contentGenerator: undefined }); // Don't store functions in DB
            } else if (existingApp.iconDataUrl !== app.iconDataUrl && (app.id === recycleBinAppId || app.id === winiaIdCreatorAppId || app.id === winiaExplorerAppId ) ) { // Keep explorer icon updated
                console.log(`Updating existing app: ${app.title}`);
                 await saveGeneratedApp({ ...existingApp, iconDataUrl: app.iconDataUrl, contentGenerator: undefined });
            } else {
                console.log(`App already exists: ${app.title}`);
            }
        } catch (error) {
            console.error(`Error ensuring ${app.title} app is in DB:`, error);
        }
    }
    console.log("Finished ensurePreinstalledAppsInDB");
}

// --- Enhanced App UI Builders ---

function buildEnhancedNotepadUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'notepad-container';
    container.style.cssText = `
        display: flex;
        flex-direction: column;
        height: 100%;
        background: #ffffff;
        font-family: 'Segoe UI', Tahoma, sans-serif;
    `;

    // Enhanced Menu Bar
    const menuBar = document.createElement('div');
    menuBar.className = 'notepad-menu-bar';
    menuBar.style.cssText = `
        background: #f0f0f0;
        border-bottom: 1px solid #ccc;
        padding: 4px;
        display: flex;
        gap: 10px;
        font-size: 11px;
    `;

    // File operations state
    let currentContent = '';
    let hasUnsavedChanges = false;
    let currentFileName = 'Untitled';

    // Create menus
    const fileMenu = createNotepadMenu('File', [
        { text: 'New', shortcut: 'Ctrl+N', action: () => newFile() },
        { text: 'Open...', shortcut: 'Ctrl+O', action: () => openFile() },
        { text: 'Save', shortcut: 'Ctrl+S', action: () => saveFile() },
        { text: 'Save As...', action: () => saveAsFile() },
        { text: '---' },
        { text: 'Print...', shortcut: 'Ctrl+P', action: () => printFile() },
        { text: '---' },
        { text: 'Exit', action: () => closeWindow(win.id) }
    ]);

    const editMenu = createNotepadMenu('Edit', [
        { text: 'Undo', shortcut: 'Ctrl+Z', action: () => document.execCommand('undo') },
        { text: 'Redo', shortcut: 'Ctrl+Y', action: () => document.execCommand('redo') },
        { text: '---' },
        { text: 'Cut', shortcut: 'Ctrl+X', action: () => document.execCommand('cut') },
        { text: 'Copy', shortcut: 'Ctrl+C', action: () => document.execCommand('copy') },
        { text: 'Paste', shortcut: 'Ctrl+V', action: () => document.execCommand('paste') },
        { text: '---' },
        { text: 'Select All', shortcut: 'Ctrl+A', action: () => textarea.select() },
        { text: 'Find...', shortcut: 'Ctrl+F', action: () => showFindDialog() }
    ]);

    const formatMenu = createNotepadMenu('Format', [
        { text: 'Word Wrap', action: () => toggleWordWrap() },
        { text: 'Font...', action: () => showFontDialog() }
    ]);

    const viewMenu = createNotepadMenu('View', [
        { text: 'Status Bar', action: () => toggleStatusBar() },
        { text: 'Zoom In', shortcut: 'Ctrl++', action: () => zoomIn() },
        { text: 'Zoom Out', shortcut: 'Ctrl+-', action: () => zoomOut() },
        { text: 'Restore Default Zoom', shortcut: 'Ctrl+0', action: () => resetZoom() }
    ]);

    menuBar.append(fileMenu, editMenu, formatMenu, viewMenu);

    // Toolbar
    const toolbar = document.createElement('div');
    toolbar.className = 'notepad-toolbar';
    toolbar.style.cssText = `
        background: #f8f8f8;
        border-bottom: 1px solid #ddd;
        padding: 6px;
        display: flex;
        gap: 4px;
        align-items: center;
    `;

    const toolbarButtons = [
        { icon: '📄', tooltip: 'New', action: () => newFile() },
        { icon: '📁', tooltip: 'Open', action: () => openFile() },
        { icon: '💾', tooltip: 'Save', action: () => saveFile() },
        { icon: '|', tooltip: '' },
        { icon: '✂️', tooltip: 'Cut', action: () => document.execCommand('cut') },
        { icon: '📋', tooltip: 'Copy', action: () => document.execCommand('copy') },
        { icon: '📌', tooltip: 'Paste', action: () => document.execCommand('paste') },
        { icon: '|', tooltip: '' },
        { icon: '🔍', tooltip: 'Find', action: () => showFindDialog() },
        { icon: '🖨️', tooltip: 'Print', action: () => printFile() }
    ];

    toolbarButtons.forEach(btn => {
        if (btn.icon === '|') {
            const separator = document.createElement('div');
            separator.style.cssText = 'width: 1px; height: 20px; background: #ccc; margin: 0 4px;';
            toolbar.appendChild(separator);
        } else {
            const button = document.createElement('button');
            button.textContent = btn.icon;
            button.title = btn.tooltip;
            button.style.cssText = `
                background: none;
                border: 1px solid transparent;
                padding: 4px 6px;
                cursor: pointer;
                border-radius: 2px;
                font-size: 12px;
            `;
            button.addEventListener('mouseenter', () => button.style.background = '#e0e0e0');
            button.addEventListener('mouseleave', () => button.style.background = 'none');
            if (btn.action) button.addEventListener('click', btn.action);
            toolbar.appendChild(button);
        }
    });

    // Main text area
    const textarea = document.createElement('textarea');
    textarea.className = 'notepad-textarea cursor-text';
    textarea.style.cssText = `
        flex: 1;
        border: none;
        outline: none;
        padding: 10px;
        font-family: 'Consolas', 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: none;
        background: white;
        color: black;
    `;
    textarea.placeholder = 'Start typing...';

    // Status bar
    const statusBar = document.createElement('div');
    statusBar.className = 'notepad-status-bar';
    statusBar.style.cssText = `
        background: #f0f0f0;
        border-top: 1px solid #ccc;
        padding: 4px 10px;
        font-size: 11px;
        display: flex;
        justify-content: space-between;
        color: #666;
    `;

    const statusLeft = document.createElement('span');
    const statusRight = document.createElement('span');
    statusBar.append(statusLeft, statusRight);

    // Update status bar
    function updateStatusBar() {
        const lines = textarea.value.split('\n').length;
        const chars = textarea.value.length;
        const words = textarea.value.trim() ? textarea.value.trim().split(/\s+/).length : 0;

        statusLeft.textContent = hasUnsavedChanges ? `${currentFileName} *` : currentFileName;
        statusRight.textContent = `Lines: ${lines} | Words: ${words} | Characters: ${chars}`;
    }

    // Event listeners
    textarea.addEventListener('input', () => {
        hasUnsavedChanges = true;
        updateStatusBar();
        updateWindowTitle();
    });

    textarea.addEventListener('keydown', (e) => {
        // Handle keyboard shortcuts
        if (e.ctrlKey) {
            switch (e.key) {
                case 'n': e.preventDefault(); newFile(); break;
                case 'o': e.preventDefault(); openFile(); break;
                case 's': e.preventDefault(); saveFile(); break;
                case 'f': e.preventDefault(); showFindDialog(); break;
                case 'p': e.preventDefault(); printFile(); break;
                case '=': case '+': e.preventDefault(); zoomIn(); break;
                case '-': e.preventDefault(); zoomOut(); break;
                case '0': e.preventDefault(); resetZoom(); break;
            }
        }
    });

    // Functions
    function updateWindowTitle() {
        const titleElement = win.element.querySelector('.winia-window-title');
        if (titleElement) {
            titleElement.textContent = `${currentFileName}${hasUnsavedChanges ? ' *' : ''} - Notepad`;
        }
    }

    function newFile() {
        if (hasUnsavedChanges) {
            if (!confirm('You have unsaved changes. Are you sure you want to create a new file?')) return;
        }
        textarea.value = '';
        currentContent = '';
        currentFileName = 'Untitled';
        hasUnsavedChanges = false;
        updateStatusBar();
        updateWindowTitle();
        playSound('click');
    }

    function openFile() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt,.md,.js,.html,.css,.json';
        input.addEventListener('change', (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    textarea.value = e.target?.result as string || '';
                    currentContent = textarea.value;
                    currentFileName = file.name;
                    hasUnsavedChanges = false;
                    updateStatusBar();
                    updateWindowTitle();
                    playSound('click');
                };
                reader.readAsText(file);
            }
        });
        input.click();
    }

    function saveFile() {
        const blob = new Blob([textarea.value], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentFileName.endsWith('.txt') ? currentFileName : currentFileName + '.txt';
        a.click();
        URL.revokeObjectURL(url);

        currentContent = textarea.value;
        hasUnsavedChanges = false;
        updateStatusBar();
        updateWindowTitle();
        playSound('click');
    }

    function saveAsFile() {
        const filename = prompt('Enter filename:', currentFileName);
        if (filename) {
            currentFileName = filename;
            saveFile();
        }
    }

    function printFile() {
        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(`
                <html>
                    <head><title>Print - ${currentFileName}</title></head>
                    <body style="font-family: 'Courier New', monospace; white-space: pre-wrap; margin: 20px;">
                        ${textarea.value.replace(/\n/g, '<br>')}
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }

    function showFindDialog() {
        const searchTerm = prompt('Find what:');
        if (searchTerm) {
            const text = textarea.value;
            const index = text.toLowerCase().indexOf(searchTerm.toLowerCase());
            if (index !== -1) {
                textarea.focus();
                textarea.setSelectionRange(index, index + searchTerm.length);
                playSound('click');
            } else {
                alert(`"${searchTerm}" not found.`);
                playSound('error');
            }
        }
    }

    let currentZoom = 12;
    function zoomIn() {
        currentZoom = Math.min(currentZoom + 2, 24);
        textarea.style.fontSize = currentZoom + 'px';
        playSound('click');
    }

    function zoomOut() {
        currentZoom = Math.max(currentZoom - 2, 8);
        textarea.style.fontSize = currentZoom + 'px';
        playSound('click');
    }

    function resetZoom() {
        currentZoom = 12;
        textarea.style.fontSize = currentZoom + 'px';
        playSound('click');
    }

    function toggleWordWrap() {
        textarea.style.whiteSpace = textarea.style.whiteSpace === 'nowrap' ? 'pre-wrap' : 'nowrap';
        playSound('click');
    }

    function toggleStatusBar() {
        statusBar.style.display = statusBar.style.display === 'none' ? 'flex' : 'none';
        playSound('click');
    }

    function showFontDialog() {
        const fonts = ['Consolas', 'Courier New', 'Arial', 'Times New Roman', 'Verdana'];
        const currentFont = textarea.style.fontFamily || 'Consolas';
        const newFont = prompt(`Choose font (${fonts.join(', ')}):`, currentFont);
        if (newFont && fonts.some(f => f.toLowerCase() === newFont.toLowerCase())) {
            textarea.style.fontFamily = newFont;
            playSound('click');
        }
    }

    // Initialize
    updateStatusBar();
    updateWindowTitle();
    setTimeout(() => textarea.focus(), 100);

    container.append(menuBar, toolbar, textarea, statusBar);
    return container;
}

function createNotepadMenu(title: string, items: Array<{text: string, shortcut?: string, action?: () => void}>): HTMLElement {
    const menu = document.createElement('div');
    menu.className = 'notepad-menu';
    menu.style.cssText = 'position: relative; display: inline-block;';

    const button = document.createElement('button');
    button.textContent = title;
    button.style.cssText = `
        background: none;
        border: none;
        padding: 4px 8px;
        cursor: pointer;
        font-size: 11px;
    `;

    const dropdown = document.createElement('div');
    dropdown.className = 'notepad-menu-dropdown';
    dropdown.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ccc;
        box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
        min-width: 150px;
        z-index: 1000;
        display: none;
    `;

    items.forEach(item => {
        if (item.text === '---') {
            const separator = document.createElement('div');
            separator.style.cssText = 'height: 1px; background: #eee; margin: 2px 0;';
            dropdown.appendChild(separator);
        } else {
            const menuItem = document.createElement('div');
            menuItem.style.cssText = `
                padding: 6px 12px;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                font-size: 11px;
            `;

            const textSpan = document.createElement('span');
            textSpan.textContent = item.text;
            menuItem.appendChild(textSpan);

            if (item.shortcut) {
                const shortcutSpan = document.createElement('span');
                shortcutSpan.textContent = item.shortcut;
                shortcutSpan.style.color = '#666';
                menuItem.appendChild(shortcutSpan);
            }

            menuItem.addEventListener('mouseenter', () => menuItem.style.background = '#e0e0e0');
            menuItem.addEventListener('mouseleave', () => menuItem.style.background = 'white');

            if (item.action) {
                menuItem.addEventListener('click', () => {
                    item.action!();
                    dropdown.style.display = 'none';
                });
            }

            dropdown.appendChild(menuItem);
        }
    });

    button.addEventListener('click', () => {
        const isVisible = dropdown.style.display === 'block';
        // Hide all other dropdowns
        document.querySelectorAll('.notepad-menu-dropdown').forEach(d => (d as HTMLElement).style.display = 'none');
        dropdown.style.display = isVisible ? 'none' : 'block';
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!menu.contains(e.target as Node)) {
            dropdown.style.display = 'none';
        }
    });

    menu.append(button, dropdown);
    return menu;
}

function buildWiniaWidgetsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'winia-widgets-container';
    container.style.cssText = `
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        height: 100%;
        overflow-y: auto;
    `;

    container.innerHTML = `
        <h2 style="margin-top: 0; text-align: center;">🧩 WiniaWidgets</h2>
        <p style="text-align: center; margin-bottom: 30px;">Pequeños gadgets de escritorio</p>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div class="widget-card" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; cursor: pointer;" data-widget="clock">
                <h3>🕐 Reloj Digital</h3>
                <p>Muestra la hora actual en el escritorio</p>
            </div>

            <div class="widget-card" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; cursor: pointer;" data-widget="calendar">
                <h3>📅 Calendario</h3>
                <p>Calendario compacto con fecha actual</p>
            </div>

            <div class="widget-card" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; cursor: pointer;" data-widget="notes">
                <h3>📝 Notas Rápidas</h3>
                <p>Bloc de notas adhesivo</p>
            </div>

            <div class="widget-card" style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; cursor: pointer;" data-widget="weather">
                <h3>🌤️ Clima</h3>
                <p>Información meteorológica</p>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <button id="clear-widgets" style="background: #ff4757; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                Limpiar Widgets del Escritorio
            </button>
        </div>
    `;

    // Add event listeners for widget cards
    container.querySelectorAll('.widget-card').forEach(card => {
        card.addEventListener('click', () => {
            const widgetType = card.getAttribute('data-widget');
            createDesktopWidget(widgetType!);
        });
    });

    // Clear widgets button
    container.querySelector('#clear-widgets')?.addEventListener('click', () => {
        document.querySelectorAll('.desktop-widget').forEach(widget => widget.remove());
    });

    return container;
}

function createDesktopWidget(type: string) {
    const widget = document.createElement('div');
    widget.className = 'desktop-widget';
    widget.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border: 1px solid #555;
        border-radius: 8px;
        padding: 10px;
        font-family: 'Segoe UI', sans-serif;
        font-size: 12px;
        cursor: move;
        z-index: 1000;
        min-width: 150px;
        backdrop-filter: blur(10px);
    `;

    // Random position
    widget.style.left = Math.random() * (window.innerWidth - 200) + 'px';
    widget.style.top = Math.random() * (window.innerHeight - 150) + 100 + 'px';

    switch (type) {
        case 'clock':
            widget.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 16px; font-weight: bold;" id="widget-time">--:--:--</div>
                    <div style="font-size: 10px;" id="widget-date">-- -- ----</div>
                </div>
            `;
            updateClockWidget(widget);
            setInterval(() => updateClockWidget(widget), 1000);
            break;

        case 'calendar':
            const now = new Date();
            widget.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-weight: bold; margin-bottom: 5px;">${now.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}</div>
                    <div style="font-size: 24px; font-weight: bold;">${now.getDate()}</div>
                    <div style="font-size: 10px;">${now.toLocaleDateString('es-ES', { weekday: 'long' })}</div>
                </div>
            `;
            break;

        case 'notes':
            widget.innerHTML = `
                <div>
                    <div style="font-weight: bold; margin-bottom: 5px;">📝 Notas</div>
                    <textarea style="width: 100%; height: 80px; background: transparent; border: 1px solid #555; color: white; font-size: 11px; resize: none;" placeholder="Escribe aquí..."></textarea>
                </div>
            `;
            widget.style.minWidth = '200px';
            break;

        case 'weather':
            widget.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-weight: bold; margin-bottom: 5px;">🌤️ Clima</div>
                    <div style="font-size: 18px;">22°C</div>
                    <div style="font-size: 10px;">Parcialmente nublado</div>
                    <div style="font-size: 10px; margin-top: 5px;">Madrid, España</div>
                </div>
            `;
            break;
    }

    // Add close button
    const closeBtn = document.createElement('div');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
        position: absolute;
        top: -5px;
        right: -5px;
        width: 16px;
        height: 16px;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
    `;
    closeBtn.addEventListener('click', () => widget.remove());
    widget.appendChild(closeBtn);

    // Make draggable
    makeWidgetDraggable(widget);

    document.body.appendChild(widget);
}

function updateClockWidget(widget: HTMLElement) {
    const now = new Date();
    const timeEl = widget.querySelector('#widget-time');
    const dateEl = widget.querySelector('#widget-date');

    if (timeEl) timeEl.textContent = now.toLocaleTimeString('es-ES');
    if (dateEl) dateEl.textContent = now.toLocaleDateString('es-ES');
}

function makeWidgetDraggable(element: HTMLElement) {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let initialX = 0;
    let initialY = 0;

    element.addEventListener('mousedown', (e) => {
        if ((e.target as HTMLElement).tagName === 'TEXTAREA') return;
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        initialX = parseInt(element.style.left) || 0;
        initialY = parseInt(element.style.top) || 0;
        element.style.zIndex = '10000';
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        e.preventDefault();
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        element.style.left = (initialX + deltaX) + 'px';
        element.style.top = (initialY + deltaY) + 'px';
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
        element.style.zIndex = '1000';
    });
}

function buildWiniaDosPromptUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'winia-dos-container';
    container.style.cssText = `
        background: #000;
        color: #00ff00;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        padding: 10px;
        height: 100%;
        overflow-y: auto;
        white-space: pre-wrap;
    `;

    const output = document.createElement('div');
    output.id = 'dos-output';

    const inputLine = document.createElement('div');
    inputLine.style.cssText = 'display: flex; align-items: center;';

    const prompt = document.createElement('span');
    prompt.textContent = 'C:\\WINIA> ';
    prompt.style.color = '#00ff00';

    const input = document.createElement('input');
    input.type = 'text';
    input.style.cssText = `
        background: transparent;
        border: none;
        color: #00ff00;
        font-family: inherit;
        font-size: inherit;
        outline: none;
        flex: 1;
    `;

    inputLine.appendChild(prompt);
    inputLine.appendChild(input);

    container.appendChild(output);
    container.appendChild(inputLine);

    // Initialize with welcome message
    const welcomeText = `WiniaDOS Prompt [Versión 1.0.0]
(c) 2024 Winia Corporation. Todos los derechos reservados.

Escribe 'help' para ver los comandos disponibles.

`;
    output.textContent = welcomeText;

    // Command history
    const commandHistory: string[] = [];
    let historyIndex = -1;

    // DOS Commands
    const dosCommands: Record<string, (args: string[]) => string> = {
        help: () => `Comandos disponibles:
  help          - Muestra esta ayuda
  dir           - Lista archivos y directorios
  cd [ruta]     - Cambia de directorio
  cls           - Limpia la pantalla
  echo [texto]  - Muestra texto
  date          - Muestra la fecha actual
  time          - Muestra la hora actual
  ver           - Muestra la versión del sistema
  launch [app]  - Lanza una aplicación
  apps          - Lista aplicaciones disponibles
  bsof          - Blue Screen of Fun
  exit          - Cierra la ventana
`,
        dir: () => `Volumen en la unidad C es WINIA
Número de serie del volumen: 1234-ABCD

Directorio de C:\\WINIA

${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}    <DIR>          .
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}    <DIR>          ..
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}    <DIR>          APPS
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}    <DIR>          SYSTEM
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}    <DIR>          USERS
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}           1,024 AUTOEXEC.BAT
${new Date().toLocaleDateString('es-ES')}  ${new Date().toLocaleTimeString('es-ES')}             512 CONFIG.SYS
               6 archivos              1,536 bytes
               3 dirs     999,999,999 bytes libres
`,
        cls: () => {
            output.textContent = '';
            return '';
        },
        echo: (args) => args.join(' '),
        date: () => `Fecha actual: ${new Date().toLocaleDateString('es-ES')}`,
        time: () => `Hora actual: ${new Date().toLocaleTimeString('es-ES')}`,
        ver: () => `WiniaDOS Versión 1.0.0
Sistema Operativo Winia [Versión 10.0.22000]`,
        cd: (args) => {
            if (args.length === 0) return 'C:\\WINIA';
            return `Directorio cambiado a: C:\\WINIA\\${args[0].toUpperCase()}`;
        },
        launch: (args) => {
            if (args.length === 0) return 'Uso: launch [nombre_app]';
            const appName = args[0].toLowerCase();
            const appMap: Record<string, string> = {
                'notepad': notepadAppId,
                'explorer': winiaExplorerAppId,
                'voyager': webVoyagerAppId,
                'widgets': winiaWidgetsAppId,
                'styler': winiaStylerAppId,
                'pixelpal': pixelPalAppId,
                'radio': winiaWavesRadioAppId
            };

            if (appMap[appName]) {
                setTimeout(() => launchAppById(appMap[appName]), 100);
                return `Lanzando ${appName}...`;
            }
            return `Aplicación '${appName}' no encontrada. Usa 'apps' para ver las disponibles.`;
        },
        apps: () => `Aplicaciones disponibles:
  notepad       - Editor de texto
  explorer      - Explorador de archivos
  voyager       - Navegador web
  widgets       - Widgets de escritorio
  styler        - Motor de temas
  pixelpal      - Mascota de escritorio
  radio         - Reproductor de radio
`,
        bsof: () => {
            setTimeout(() => showBlueScreenOfFun(), 100);
            return 'Iniciando Blue Screen of Fun...';
        },
        exit: () => {
            setTimeout(() => closeWindow(win.id), 100);
            return 'Cerrando WiniaDOS Prompt...';
        }
    };

    // Handle input
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            const command = input.value.trim();
            if (command) {
                commandHistory.unshift(command);
                historyIndex = -1;

                // Add command to output
                output.textContent += `C:\\WINIA> ${command}\n`;

                // Parse and execute command
                const parts = command.split(' ');
                const cmd = parts[0].toLowerCase();
                const args = parts.slice(1);

                let result = '';
                if (dosCommands[cmd]) {
                    result = dosCommands[cmd](args);
                } else {
                    result = `'${cmd}' no se reconoce como un comando interno o externo,\nprograma o archivo por lotes ejecutable.`;
                }

                if (result) {
                    output.textContent += result + '\n';
                }
                output.textContent += '\n';

                // Scroll to bottom
                container.scrollTop = container.scrollHeight;
            }
            input.value = '';
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                historyIndex++;
                input.value = commandHistory[historyIndex];
            }
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (historyIndex > 0) {
                historyIndex--;
                input.value = commandHistory[historyIndex];
            } else if (historyIndex === 0) {
                historyIndex = -1;
                input.value = '';
            }
        }
    });

    // Focus input when container is clicked
    container.addEventListener('click', () => input.focus());

    // Auto-focus
    setTimeout(() => input.focus(), 100);

    return container;
}

function showBlueScreenOfFun() {
    const bsof = document.createElement('div');
    bsof.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: #0078d4;
        color: white;
        font-family: 'Segoe UI', sans-serif;
        z-index: 999999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 40px;
        box-sizing: border-box;
    `;

    bsof.innerHTML = `
        <div style="font-size: 120px; margin-bottom: 30px;">😄</div>
        <h1 style="font-size: 48px; margin: 0 0 20px 0; font-weight: 300;">¡Oops!</h1>
        <p style="font-size: 24px; margin: 0 0 40px 0; max-width: 800px; line-height: 1.4;">
            Tu PC se encontró con un problema y necesita reiniciarse... ¡pero es solo una broma!
            Este es el "Blue Screen of Fun" de Winia.
        </p>
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-bottom: 40px;">
            <p style="font-family: 'Courier New', monospace; font-size: 14px; margin: 0;">
                BLUE_SCREEN_OF_FUN<br>
                Error Code: 0x00000FUN<br>
                Winia Version: 1.0.0<br>
                Uptime: ${Math.floor(Math.random() * 1000)} minutes
            </p>
        </div>
        <p style="font-size: 18px; margin: 0 0 20px 0;">
            No te preocupes, esto es solo un easter egg divertido.
        </p>
        <button id="bsof-close" style="
            background: white;
            color: #0078d4;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        ">Volver a Winia</button>
    `;

    document.body.appendChild(bsof);

    // Add click handler to close button
    bsof.querySelector('#bsof-close')?.addEventListener('click', () => {
        bsof.remove();
    });

    // Auto-close after 10 seconds
    setTimeout(() => {
        if (document.body.contains(bsof)) {
            bsof.remove();
        }
    }, 10000);
}

function buildPixelPalUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'pixel-pal-container';
    container.style.cssText = `
        padding: 20px;
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
        color: #2d3436;
        height: 100%;
        text-align: center;
    `;

    container.innerHTML = `
        <h2 style="margin-top: 0;">🐾 PixelPal</h2>
        <p>Tu mascota de escritorio animada</p>

        <div style="margin: 30px 0;">
            <div id="pal-preview" style="font-size: 64px; margin: 20px 0;">😸</div>
            <p>Estado: <span id="pal-status">Durmiendo</span></p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; max-width: 300px; margin: 0 auto;">
            <button class="pal-btn" data-action="spawn" style="background: #00b894; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                Invocar PixelPal
            </button>
            <button class="pal-btn" data-action="feed" style="background: #fdcb6e; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                Alimentar
            </button>
            <button class="pal-btn" data-action="play" style="background: #6c5ce7; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                Jugar
            </button>
            <button class="pal-btn" data-action="sleep" style="background: #74b9ff; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                Dormir
            </button>
        </div>

        <div style="margin-top: 30px;">
            <h3>Configuración</h3>
            <label style="display: block; margin: 10px 0;">
                Tipo de mascota:
                <select id="pal-type" style="margin-left: 10px; padding: 5px;">
                    <option value="😸">Gato</option>
                    <option value="🐶">Perro</option>
                    <option value="🐰">Conejo</option>
                    <option value="🐸">Rana</option>
                    <option value="🦄">Unicornio</option>
                </select>
            </label>
        </div>
    `;

    let currentPal: HTMLElement | null = null;
    let palState = 'sleeping';
    const statusEl = container.querySelector('#pal-status') as HTMLElement;
    const previewEl = container.querySelector('#pal-preview') as HTMLElement;
    const typeSelect = container.querySelector('#pal-type') as HTMLSelectElement;

    // Update preview when type changes
    typeSelect.addEventListener('change', () => {
        previewEl.textContent = typeSelect.value;
        if (currentPal) {
            currentPal.textContent = typeSelect.value;
        }
    });

    // Button handlers
    container.querySelectorAll('.pal-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const action = btn.getAttribute('data-action');

            switch (action) {
                case 'spawn':
                    if (currentPal) {
                        currentPal.remove();
                    }
                    currentPal = createPixelPal(typeSelect.value);
                    palState = 'active';
                    statusEl.textContent = 'Activo';
                    break;

                case 'feed':
                    if (currentPal) {
                        animatePixelPal(currentPal, 'feed');
                        palState = 'happy';
                        statusEl.textContent = 'Feliz';
                    }
                    break;

                case 'play':
                    if (currentPal) {
                        animatePixelPal(currentPal, 'play');
                        palState = 'playing';
                        statusEl.textContent = 'Jugando';
                    }
                    break;

                case 'sleep':
                    if (currentPal) {
                        animatePixelPal(currentPal, 'sleep');
                        palState = 'sleeping';
                        statusEl.textContent = 'Durmiendo';
                    }
                    break;
            }
        });
    });

    return container;
}

function createPixelPal(emoji: string): HTMLElement {
    const pal = document.createElement('div');
    pal.className = 'pixel-pal';
    pal.textContent = emoji;
    pal.style.cssText = `
        position: fixed;
        font-size: 32px;
        z-index: 1000;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;
        animation: palFloat 3s ease-in-out infinite;
    `;

    // Random position
    pal.style.left = Math.random() * (window.innerWidth - 50) + 'px';
    pal.style.top = Math.random() * (window.innerHeight - 100) + 100 + 'px';

    // Add floating animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes palFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes palBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        @keyframes palSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    // Make draggable
    makeWidgetDraggable(pal);

    // Random movement
    setInterval(() => {
        if (Math.random() < 0.3) { // 30% chance to move
            const newX = Math.random() * (window.innerWidth - 50);
            const newY = Math.random() * (window.innerHeight - 100) + 100;
            pal.style.left = newX + 'px';
            pal.style.top = newY + 'px';
        }
    }, 5000);

    document.body.appendChild(pal);
    return pal;
}

function animatePixelPal(pal: HTMLElement, action: string) {
    switch (action) {
        case 'feed':
            pal.style.animation = 'palBounce 0.5s ease-in-out 3';
            break;
        case 'play':
            pal.style.animation = 'palSpin 1s ease-in-out 2';
            break;
        case 'sleep':
            pal.style.animation = 'palFloat 2s ease-in-out infinite';
            pal.style.opacity = '0.7';
            break;
    }

    setTimeout(() => {
        pal.style.animation = 'palFloat 3s ease-in-out infinite';
        pal.style.opacity = '1';
    }, 2000);
}

function buildWiniaStylerUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'winia-styler-container';
    container.style.cssText = `
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        height: 100%;
        overflow-y: auto;
    `;

    container.innerHTML = `
        <h2 style="margin-top: 0; text-align: center;">🎨 WiniaStyler Engine</h2>
        <p style="text-align: center; margin-bottom: 30px;">Motor de temas avanzado</p>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <div>
                <h3>🎨 Colores del Sistema</h3>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin: 10px 0;">
                        Color Primario:
                        <input type="color" id="primary-color" value="#0078d4" style="margin-left: 10px; width: 50px; height: 30px;">
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        Color Secundario:
                        <input type="color" id="secondary-color" value="#106ebe" style="margin-left: 10px; width: 50px; height: 30px;">
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        Color de Acento:
                        <input type="color" id="accent-color" value="#005a9e" style="margin-left: 10px; width: 50px; height: 30px;">
                    </label>
                </div>

                <h3>🖼️ Fondo de Escritorio</h3>
                <div style="margin: 15px 0;">
                    <select id="wallpaper-select" style="width: 100%; padding: 8px; margin: 10px 0;">
                        <option value="default">Fondo por defecto</option>
                        <option value="gradient1">Gradiente Azul</option>
                        <option value="gradient2">Gradiente Púrpura</option>
                        <option value="gradient3">Gradiente Verde</option>
                        <option value="pattern1">Patrón Geométrico</option>
                        <option value="solid">Color Sólido</option>
                    </select>
                    <input type="color" id="bg-color" value="#1e3a8a" style="width: 100%; height: 40px; margin: 10px 0;">
                </div>
            </div>

            <div>
                <h3>🔤 Tipografía</h3>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin: 10px 0;">
                        Fuente del Sistema:
                        <select id="font-family" style="width: 100%; padding: 8px; margin: 5px 0;">
                            <option value="Segoe UI">Segoe UI</option>
                            <option value="Arial">Arial</option>
                            <option value="Helvetica">Helvetica</option>
                            <option value="Times New Roman">Times New Roman</option>
                            <option value="Courier New">Courier New</option>
                            <option value="Comic Sans MS">Comic Sans MS</option>
                        </select>
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        Tamaño de Fuente:
                        <input type="range" id="font-size" min="12" max="20" value="14" style="width: 100%; margin: 5px 0;">
                        <span id="font-size-value">14px</span>
                    </label>
                </div>

                <h3>✨ Efectos Visuales</h3>
                <div style="margin: 15px 0;">
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="blur-effects" checked> Efectos de desenfoque
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="animations" checked> Animaciones
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        <input type="checkbox" id="shadows" checked> Sombras
                    </label>
                    <label style="display: block; margin: 10px 0;">
                        Transparencia:
                        <input type="range" id="transparency" min="0" max="100" value="10" style="width: 100%; margin: 5px 0;">
                        <span id="transparency-value">10%</span>
                    </label>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button id="apply-theme" style="background: #00b894; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; margin: 0 10px; font-size: 16px;">
                Aplicar Tema
            </button>
            <button id="reset-theme" style="background: #ff4757; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; margin: 0 10px; font-size: 16px;">
                Restablecer
            </button>
            <button id="save-theme" style="background: #3742fa; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; margin: 0 10px; font-size: 16px;">
                Guardar Tema
            </button>
        </div>

        <div style="margin-top: 20px; text-align: center;">
            <h3>🎭 Temas Predefinidos</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                <button class="preset-theme" data-theme="dark" style="background: #2d3436; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                    Oscuro
                </button>
                <button class="preset-theme" data-theme="light" style="background: #ddd; color: #333; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                    Claro
                </button>
                <button class="preset-theme" data-theme="neon" style="background: #ff006e; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                    Neón
                </button>
                <button class="preset-theme" data-theme="retro" style="background: #8b5a3c; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">
                    Retro
                </button>
            </div>
        </div>
    `;

    // Event listeners
    const fontSizeSlider = container.querySelector('#font-size') as HTMLInputElement;
    const fontSizeValue = container.querySelector('#font-size-value') as HTMLElement;
    const transparencySlider = container.querySelector('#transparency') as HTMLInputElement;
    const transparencyValue = container.querySelector('#transparency-value') as HTMLElement;

    fontSizeSlider.addEventListener('input', () => {
        fontSizeValue.textContent = fontSizeSlider.value + 'px';
    });

    transparencySlider.addEventListener('input', () => {
        transparencyValue.textContent = transparencySlider.value + '%';
    });

    // Apply theme button
    container.querySelector('#apply-theme')?.addEventListener('click', () => {
        applyCustomTheme(container);
    });

    // Reset theme button
    container.querySelector('#reset-theme')?.addEventListener('click', () => {
        resetToDefaultTheme();
    });

    // Preset themes
    container.querySelectorAll('.preset-theme').forEach(btn => {
        btn.addEventListener('click', () => {
            const theme = btn.getAttribute('data-theme');
            applyPresetTheme(theme!);
        });
    });

    return container;
}

function applyCustomTheme(container: HTMLElement) {
    const primaryColor = (container.querySelector('#primary-color') as HTMLInputElement).value;
    const secondaryColor = (container.querySelector('#secondary-color') as HTMLInputElement).value;
    const accentColor = (container.querySelector('#accent-color') as HTMLInputElement).value;
    const wallpaper = (container.querySelector('#wallpaper-select') as HTMLSelectElement).value;
    const bgColor = (container.querySelector('#bg-color') as HTMLInputElement).value;
    const fontFamily = (container.querySelector('#font-family') as HTMLSelectElement).value;
    const fontSize = (container.querySelector('#font-size') as HTMLInputElement).value;
    const blurEffects = (container.querySelector('#blur-effects') as HTMLInputElement).checked;
    const animations = (container.querySelector('#animations') as HTMLInputElement).checked;
    const shadows = (container.querySelector('#shadows') as HTMLInputElement).checked;
    const transparency = (container.querySelector('#transparency') as HTMLInputElement).value;

    // Apply wallpaper
    const desktop = document.getElementById('winia-desktop');
    if (desktop) {
        switch (wallpaper) {
            case 'gradient1':
                desktop.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                break;
            case 'gradient2':
                desktop.style.background = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
                break;
            case 'gradient3':
                desktop.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                break;
            case 'pattern1':
                desktop.style.background = `${bgColor} url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`;
                break;
            case 'solid':
                desktop.style.background = bgColor;
                break;
            default:
                desktop.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)';
        }
    }

    // Apply font settings
    document.body.style.fontFamily = fontFamily;
    document.body.style.fontSize = fontSize + 'px';

    // Apply color scheme
    document.documentElement.style.setProperty('--primary-color', primaryColor);
    document.documentElement.style.setProperty('--secondary-color', secondaryColor);
    document.documentElement.style.setProperty('--accent-color', accentColor);

    // Apply effects
    const windows = document.querySelectorAll('.window');
    windows.forEach(win => {
        const winEl = win as HTMLElement;
        if (blurEffects) {
            winEl.style.backdropFilter = 'blur(10px)';
        } else {
            winEl.style.backdropFilter = 'none';
        }

        if (shadows) {
            winEl.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
        } else {
            winEl.style.boxShadow = 'none';
        }

        winEl.style.opacity = (100 - parseInt(transparency)) / 100 + '';
    });

    // Apply animations
    if (!animations) {
        const style = document.createElement('style');
        style.textContent = '* { transition: none !important; animation: none !important; }';
        document.head.appendChild(style);
    }
}

function applyPresetTheme(theme: string) {
    const desktop = document.getElementById('winia-desktop');
    if (!desktop) return;

    switch (theme) {
        case 'dark':
            desktop.style.background = 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)';
            document.body.style.fontFamily = 'Segoe UI';
            break;
        case 'light':
            desktop.style.background = 'linear-gradient(135deg, #ddd6fe 0%, #e0e7ff 100%)';
            document.body.style.fontFamily = 'Segoe UI';
            break;
        case 'neon':
            desktop.style.background = 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)';
            document.body.style.fontFamily = 'Arial';
            break;
        case 'retro':
            desktop.style.background = 'linear-gradient(135deg, #8b5a3c 0%, #d4a574 100%)';
            document.body.style.fontFamily = 'Courier New';
            break;
    }
}

function resetToDefaultTheme() {
    const desktop = document.getElementById('winia-desktop');
    if (desktop) {
        desktop.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)';
    }
    document.body.style.fontFamily = 'Segoe UI, sans-serif';
    document.body.style.fontSize = '14px';

    // Remove custom styles
    const customStyles = document.querySelectorAll('style');
    customStyles.forEach(style => {
        if (style.textContent?.includes('transition: none')) {
            style.remove();
        }
    });
}

// Stub functions for remaining apps - will be implemented in next chunks
function buildWiniaSoundRecUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
        <div style="padding: 20px; text-align: center; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; height: 100%;">
            <h2>🎙️ WiniaSoundRec</h2>
            <p>Grabadora de sonidos del sistema</p>
            <div style="margin: 30px 0;">
                <button style="background: #ff4757; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ⏺️ REC
                </button>
                <button style="background: #2f3542; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ⏹️ STOP
                </button>
                <button style="background: #3742fa; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ▶️ PLAY
                </button>
            </div>
            <p>Estado: Listo para grabar</p>
            <p style="font-size: 12px; margin-top: 20px;">Funcionalidad completa próximamente...</p>
        </div>
    `;
    return container;
}

function buildWiniaSoundPlayerUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
        <div style="padding: 20px; text-align: center; background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%); color: white; height: 100%;">
            <h2>🔊 WiniaSound Player</h2>
            <p>Reproductor de sonidos del sistema</p>
            <div style="margin: 30px 0;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h3>🎵 Ahora reproduciendo</h3>
                    <p>Sonido de inicio de Winia</p>
                    <div style="background: rgba(255,255,255,0.2); height: 4px; border-radius: 2px; margin: 10px 0;">
                        <div style="background: white; height: 100%; width: 30%; border-radius: 2px;"></div>
                    </div>
                </div>
                <button style="background: #3742fa; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ⏮️
                </button>
                <button style="background: #00d2d3; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ▶️
                </button>
                <button style="background: #3742fa; color: white; border: none; padding: 15px 30px; border-radius: 50%; font-size: 18px; cursor: pointer; margin: 10px;">
                    ⏭️
                </button>
            </div>
            <p style="font-size: 12px; margin-top: 20px;">Funcionalidad completa próximamente...</p>
        </div>
    `;
    return container;
}

function buildCodeGenieHubUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
        <div style="padding: 20px; background: linear-gradient(135deg, #7209b7 0%, #2196f3 100%); color: white; height: 100%; overflow-y: auto;">
            <h2 style="text-align: center;">🧞‍♂️ CodeGenie App Hub</h2>
            <p style="text-align: center; margin-bottom: 30px;">Centro de aplicaciones generadas</p>

            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🎯 Aplicaciones Destacadas</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4>📊 Dashboard Analytics</h4>
                        <p style="font-size: 12px;">Panel de control con gráficos</p>
                        <button style="background: #00b894; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            Instalar
                        </button>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <h4>🎮 Mini Juegos</h4>
                        <p style="font-size: 12px;">Colección de juegos retro</p>
                        <button style="background: #00b894; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            Instalar
                        </button>
                    </div>
                </div>
            </div>

            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🔧 Herramientas de Desarrollo</h3>
                <p>Crea tus propias aplicaciones con CodeGenie</p>
                <button style="background: #3742fa; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin-top: 10px;">
                    Abrir CodeGenie
                </button>
            </div>

            <p style="font-size: 12px; text-align: center; margin-top: 30px; opacity: 0.8;">
                Funcionalidad completa próximamente...
            </p>
        </div>
    `;
    return container;
}

function buildRetroFxLabUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.innerHTML = `
        <div style="padding: 20px; background: linear-gradient(135deg, #ff006e 0%, #8338ec 100%); color: white; height: 100%; overflow-y: auto;">
            <h2 style="text-align: center;">🧪 RetroFX Lab</h2>
            <p style="text-align: center; margin-bottom: 30px;">Laboratorio de efectos visuales retro</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>🎨 Efectos Disponibles</h3>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4>Pixel Dissolve</h4>
                        <p style="font-size: 12px;">Efecto de disolución en bloques 4x4px</p>
                        <button class="fx-test" data-effect="pixelDissolve" style="background: #00b894; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                            Probar
                        </button>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4>Scanlines</h4>
                        <p style="font-size: 12px;">Líneas de escaneo retro</p>
                        <button class="fx-test" data-effect="scanlines" style="background: #00b894; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                            Probar
                        </button>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4>CRT Glow</h4>
                        <p style="font-size: 12px;">Efecto de monitor CRT</p>
                        <button class="fx-test" data-effect="crtGlow" style="background: #00b894; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                            Probar
                        </button>
                    </div>
                </div>

                <div>
                    <h3>🎛️ Configuración</h3>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                        <label style="display: block; margin: 10px 0;">
                            Duración (ms):
                            <input type="range" min="500" max="3000" value="1500" style="width: 100%; margin: 5px 0;">
                            <span>1500ms</span>
                        </label>
                        <label style="display: block; margin: 10px 0;">
                            Intensidad:
                            <input type="range" min="1" max="10" value="5" style="width: 100%; margin: 5px 0;">
                            <span>5</span>
                        </label>
                        <label style="display: block; margin: 10px 0;">
                            <input type="checkbox" checked> Ease-in-out
                        </label>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4>Vista Previa</h4>
                        <div id="fx-preview" style="width: 100%; height: 100px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                            Elemento de Prueba
                        </div>
                    </div>
                </div>
            </div>

            <p style="font-size: 12px; text-align: center; margin-top: 30px; opacity: 0.8;">
                Funcionalidad completa próximamente...
            </p>
        </div>
    `;

    // Add event listeners for effect testing
    container.querySelectorAll('.fx-test').forEach(btn => {
        btn.addEventListener('click', () => {
            const effect = btn.getAttribute('data-effect');
            const preview = container.querySelector('#fx-preview') as HTMLElement;
            testRetroEffect(preview, effect!);
        });
    });

    return container;
}

function testRetroEffect(element: HTMLElement, effect: string) {
    switch (effect) {
        case 'pixelDissolve':
            element.style.animation = 'pixelDissolve 1.5s ease-in-out';
            break;
        case 'scanlines':
            element.style.background = 'linear-gradient(45deg, #667eea, #764ba2), repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(255,255,255,0.1) 2px, rgba(255,255,255,0.1) 4px)';
            setTimeout(() => {
                element.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
            }, 2000);
            break;
        case 'crtGlow':
            element.style.boxShadow = '0 0 20px #00ff00, inset 0 0 20px rgba(0,255,0,0.2)';
            element.style.filter = 'brightness(1.2) contrast(1.1)';
            setTimeout(() => {
                element.style.boxShadow = 'none';
                element.style.filter = 'none';
            }, 2000);
            break;
    }
}

function buildWiniaWavesRadioUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'winia-waves-radio-container';
    container.style.cssText = `
        padding: 15px;
        background: linear-gradient(135deg, #8b4513 0%, #d2691e 100%);
        color: white;
        height: 100%;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        box-sizing: border-box;
    `;

    // Radio state
    let currentStation: any = null;
    let isPlaying = false;
    let volume = 50;
    let favoriteStations: any[] = [];
    let allStations: any[] = [];
    let filteredStations: any[] = [];
    let currentCategory = 'spain';
    let audioElement: HTMLAudioElement | null = null;
    let presetStations: any[] = [null, null, null, null];

    // Load favorites and presets from localStorage
    try {
        const savedFavorites = localStorage.getItem('winiaWavesFavorites');
        if (savedFavorites) favoriteStations = JSON.parse(savedFavorites);

        const savedPresets = localStorage.getItem('winiaWavesPresets');
        if (savedPresets) presetStations = JSON.parse(savedPresets);
    } catch (e) {
        console.warn('Could not load radio settings:', e);
    }

    // Create main structure
    container.innerHTML = `
        <div style="text-align: center; margin-bottom: 15px;">
            <h2 style="margin: 0; font-size: 18px;">📻 Winia Waves Radio</h2>
            <p style="margin: 5px 0; font-size: 11px;">Sintoniza el mundo retro</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
            <!-- Radio Physical Interface -->
            <div id="radio-interface" style="background: linear-gradient(145deg, #654321, #8b4513); padding: 15px; border-radius: 12px; border: 3px solid #4a4a4a; box-shadow: inset 0 0 15px rgba(0,0,0,0.3);">
                <div style="text-align: center; margin-bottom: 12px;">
                    <div style="background: #000; color: #00ff00; padding: 8px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 12px; border: 2px inset #666; min-height: 35px; display: flex; flex-direction: column; justify-content: center;">
                        <div id="radio-display">WINIA WAVES</div>
                        <div style="font-size: 9px; margin-top: 2px;" id="radio-status">● LISTO</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                    <button id="play-btn" style="background: radial-gradient(circle, #ff4444, #cc0000); color: white; border: 3px outset #666; border-radius: 50%; width: 40px; height: 40px; font-size: 14px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                        ▶️
                    </button>
                    <button id="stop-btn" style="background: radial-gradient(circle, #444444, #222222); color: white; border: 3px outset #666; border-radius: 50%; width: 40px; height: 40px; font-size: 14px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                        ⏹️
                    </button>
                </div>

                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 3px; font-size: 9px;">VOLUMEN</label>
                    <input id="volume-slider" type="range" min="0" max="100" value="50" style="width: 100%; background: #333; border-radius: 8px;">
                    <div style="text-align: center; font-size: 9px; margin-top: 2px;" id="volume-display">50%</div>
                </div>

                <div style="display: flex; gap: 2px; justify-content: center; margin-bottom: 8px;">
                    <button class="preset-btn" data-preset="0" style="background: #666; color: white; border: 2px outset #888; padding: 2px 4px; font-size: 8px; border-radius: 2px; cursor: pointer;">P1</button>
                    <button class="preset-btn" data-preset="1" style="background: #666; color: white; border: 2px outset #888; padding: 2px 4px; font-size: 8px; border-radius: 2px; cursor: pointer;">P2</button>
                    <button class="preset-btn" data-preset="2" style="background: #666; color: white; border: 2px outset #888; padding: 2px 4px; font-size: 8px; border-radius: 2px; cursor: pointer;">P3</button>
                    <button class="preset-btn" data-preset="3" style="background: #666; color: white; border: 2px outset #888; padding: 2px 4px; font-size: 8px; border-radius: 2px; cursor: pointer;">P4</button>
                </div>

                <div style="text-align: center;">
                    <button id="save-preset-btn" style="background: #4CAF50; color: white; border: none; padding: 3px 6px; border-radius: 2px; cursor: pointer; font-size: 8px;">
                        💾 Guardar
                    </button>
                </div>
            </div>

            <!-- Station List -->
            <div style="background: rgba(0,0,0,0.3); padding: 12px; border-radius: 8px;">
                <h3 style="margin: 0 0 8px 0; text-align: center; font-size: 12px;">📡 Emisoras</h3>

                <div style="margin-bottom: 8px;">
                    <select id="category-select" style="width: 100%; padding: 3px; background: #333; color: white; border: 1px solid #666; border-radius: 2px; font-size: 9px;">
                        <option value="spain">🇪🇸 España</option>
                        <option value="international">🌍 Internacional</option>
                        <option value="lofi">🎵 Lofi Hip Hop</option>
                        <option value="jazz">🎷 Jazz</option>
                        <option value="rock">🎸 Rock</option>
                        <option value="electronic">🎹 Electrónica</option>
                        <option value="favorites">⭐ Favoritos</option>
                    </select>
                </div>

                <div style="margin-bottom: 8px;">
                    <input id="search-input" type="text" placeholder="Buscar emisora..." style="width: 100%; padding: 3px; background: #333; color: white; border: 1px solid #666; border-radius: 2px; font-size: 9px; box-sizing: border-box;">
                </div>

                <div id="stations-list" style="max-height: 140px; overflow-y: auto; border: 1px solid #666; border-radius: 3px; background: rgba(0,0,0,0.2);">
                    <div style="padding: 15px; text-align: center; color: #ccc;">
                        <div>🔄 Cargando emisoras...</div>
                        <div style="font-size: 8px; margin-top: 3px;">Conectando con radio-browser.info</div>
                    </div>
                </div>

                <div style="margin-top: 8px; text-align: center;">
                    <button id="refresh-btn" style="background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 8px;">
                        🔄 Actualizar
                    </button>
                </div>
            </div>
        </div>

        <!-- Now Playing Bar -->
        <div id="now-playing" style="padding: 10px; background: rgba(0,0,0,0.2); border-radius: 8px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="font-size: 9px; flex: 1;">
                    <div>🎵 Emisora:</div>
                    <div style="font-weight: bold; margin-top: 2px;" id="current-station-name">Ninguna seleccionada</div>
                </div>
                <div id="equalizer" style="display: flex; gap: 2px; align-items: center; flex: 0 0 auto;">
                    <div style="width: 3px; height: 8px; background: #666; border-radius: 1px;" class="eq-bar"></div>
                    <div style="width: 3px; height: 6px; background: #666; border-radius: 1px;" class="eq-bar"></div>
                    <div style="width: 3px; height: 10px; background: #666; border-radius: 1px;" class="eq-bar"></div>
                    <div style="width: 3px; height: 7px; background: #666; border-radius: 1px;" class="eq-bar"></div>
                    <div style="width: 3px; height: 9px; background: #666; border-radius: 1px;" class="eq-bar"></div>
                </div>
                <div style="font-size: 9px; text-align: right; flex: 1;">
                    <div>📡 Estado:</div>
                    <div style="font-weight: bold; margin-top: 2px;" id="connection-status">DESCONECTADO</div>
                </div>
            </div>
        </div>

        <style>
            .eq-bar {
                transition: all 0.3s ease;
            }
            .eq-bar.active {
                background: #00ff00 !important;
                animation: pulse 0.8s infinite alternate;
            }
            @keyframes pulse {
                0% { opacity: 0.4; transform: scaleY(0.8); }
                100% { opacity: 1; transform: scaleY(1.2); }
            }
            .station-item {
                padding: 4px;
                border-bottom: 1px solid #666;
                cursor: pointer;
                transition: background 0.2s;
                font-size: 9px;
            }
            .station-item:hover {
                background: rgba(255,255,255,0.1);
            }
            .station-item.selected {
                background: rgba(0,128,255,0.3);
            }
            .station-item.favorite {
                border-left: 2px solid #FFD700;
            }
        </style>
    `;

    // Get DOM elements
    const playBtn = container.querySelector('#play-btn') as HTMLButtonElement;
    const stopBtn = container.querySelector('#stop-btn') as HTMLButtonElement;
    const volumeSlider = container.querySelector('#volume-slider') as HTMLInputElement;
    const volumeDisplay = container.querySelector('#volume-display') as HTMLElement;
    const categorySelect = container.querySelector('#category-select') as HTMLSelectElement;
    const searchInput = container.querySelector('#search-input') as HTMLInputElement;
    const stationsList = container.querySelector('#stations-list') as HTMLElement;
    const refreshBtn = container.querySelector('#refresh-btn') as HTMLButtonElement;
    const radioDisplay = container.querySelector('#radio-display') as HTMLElement;
    const radioStatus = container.querySelector('#radio-status') as HTMLElement;
    const currentStationName = container.querySelector('#current-station-name') as HTMLElement;
    const connectionStatus = container.querySelector('#connection-status') as HTMLElement;
    const eqBars = container.querySelectorAll('.eq-bar') as NodeListOf<HTMLElement>;
    const presetBtns = container.querySelectorAll('.preset-btn') as NodeListOf<HTMLButtonElement>;
    const savePresetBtn = container.querySelector('#save-preset-btn') as HTMLButtonElement;

    // API functions for radio-browser.info
    async function fetchRadioStations(category: string, searchTerm: string = ''): Promise<any[]> {
        try {
            let url = 'https://de1.api.radio-browser.info/json/stations/';
            const params = new URLSearchParams();

            switch (category) {
                case 'spain':
                    params.append('countrycode', 'ES');
                    params.append('limit', '50');
                    break;
                case 'lofi':
                    params.append('tag', 'lofi');
                    params.append('limit', '30');
                    break;
                case 'jazz':
                    params.append('tag', 'jazz');
                    params.append('limit', '30');
                    break;
                case 'rock':
                    params.append('tag', 'rock');
                    params.append('limit', '30');
                    break;
                case 'electronic':
                    params.append('tag', 'electronic');
                    params.append('limit', '30');
                    break;
                case 'international':
                    params.append('limit', '50');
                    break;
                case 'favorites':
                    return favoriteStations;
            }

            if (searchTerm) {
                params.append('name', searchTerm);
            }

            url += 'search?' + params.toString();

            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'WiniaWaves/1.0'
                }
            });

            if (!response.ok) throw new Error('Network error');

            const stations = await response.json();
            return stations.filter((station: any) => station.url_resolved && station.name);
        } catch (error) {
            console.warn('Error fetching radio stations:', error);
            return [];
        }
    }

    // Update stations list
    async function updateStationsList() {
        stationsList.innerHTML = '<div style="padding: 15px; text-align: center; color: #ccc;"><div>🔄 Cargando...</div></div>';

        try {
            const searchTerm = searchInput.value.trim();
            const stations = await fetchRadioStations(currentCategory, searchTerm);

            if (stations.length === 0) {
                stationsList.innerHTML = '<div style="padding: 15px; text-align: center; color: #ccc;"><div>😔 No se encontraron emisoras</div></div>';
                return;
            }

            allStations = stations;
            filteredStations = stations;
            renderStationsList();
        } catch (error) {
            stationsList.innerHTML = '<div style="padding: 15px; text-align: center; color: #ff6666;"><div>❌ Error de conexión</div></div>';
        }
    }

    function renderStationsList() {
        stationsList.innerHTML = '';

        filteredStations.forEach((station, index) => {
            const stationEl = document.createElement('div');
            stationEl.className = 'station-item';
            if (favoriteStations.some(fav => fav.stationuuid === station.stationuuid)) {
                stationEl.classList.add('favorite');
            }
            if (currentStation && currentStation.stationuuid === station.stationuuid) {
                stationEl.classList.add('selected');
            }

            const countryFlag = getCountryFlag(station.countrycode);
            const bitrate = station.bitrate ? `${station.bitrate}kbps` : '';

            stationEl.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="flex: 1; min-width: 0;">
                        <div style="font-weight: bold; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            ${countryFlag} ${station.name}
                        </div>
                        <div style="font-size: 8px; color: #ccc; margin-top: 1px;">
                            ${station.tags || 'General'} ${bitrate ? '• ' + bitrate : ''}
                        </div>
                    </div>
                    <div style="display: flex; gap: 2px; align-items: center;">
                        <button class="fav-btn" style="background: none; border: none; color: ${favoriteStations.some(fav => fav.stationuuid === station.stationuuid) ? '#FFD700' : '#666'}; cursor: pointer; font-size: 10px; padding: 2px;">
                            ⭐
                        </button>
                        <div style="font-size: 10px;">📻</div>
                    </div>
                </div>
            `;

            stationEl.addEventListener('click', (e) => {
                if (!(e.target as HTMLElement).classList.contains('fav-btn')) {
                    selectStation(station);
                }
            });

            const favBtn = stationEl.querySelector('.fav-btn') as HTMLButtonElement;
            favBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(station);
            });

            stationsList.appendChild(stationEl);
        });
    }

    function getCountryFlag(countryCode: string): string {
        const flags: Record<string, string> = {
            'ES': '🇪🇸', 'US': '🇺🇸', 'GB': '🇬🇧', 'FR': '🇫🇷', 'DE': '🇩🇪', 'IT': '🇮🇹',
            'JP': '🇯🇵', 'CA': '🇨🇦', 'AU': '🇦🇺', 'BR': '🇧🇷', 'MX': '🇲🇽', 'AR': '🇦🇷'
        };
        return flags[countryCode] || '🌍';
    }

    function toggleFavorite(station: any) {
        const index = favoriteStations.findIndex(fav => fav.stationuuid === station.stationuuid);
        if (index >= 0) {
            favoriteStations.splice(index, 1);
        } else {
            favoriteStations.push(station);
        }

        try {
            localStorage.setItem('winiaWavesFavorites', JSON.stringify(favoriteStations));
        } catch (e) {
            console.warn('Could not save favorites:', e);
        }

        renderStationsList();
    }

    function selectStation(station: any) {
        currentStation = station;
        radioDisplay.textContent = station.name;
        currentStationName.textContent = station.name;
        renderStationsList();
    }

    function playStation() {
        if (!currentStation) return;

        if (audioElement) {
            audioElement.pause();
            audioElement = null;
        }

        radioStatus.textContent = '● CONECTANDO...';
        connectionStatus.textContent = 'CONECTANDO';
        connectionStatus.style.color = '#ffaa00';

        // Animate equalizer
        eqBars.forEach(bar => bar.classList.remove('active'));

        audioElement = new Audio();
        audioElement.crossOrigin = 'anonymous';
        audioElement.volume = volume / 100;

        audioElement.addEventListener('loadstart', () => {
            radioStatus.textContent = '● CARGANDO...';
        });

        audioElement.addEventListener('canplay', () => {
            radioStatus.textContent = '● EN VIVO';
            connectionStatus.textContent = 'CONECTADO';
            connectionStatus.style.color = '#00ff00';
            isPlaying = true;

            // Animate equalizer
            eqBars.forEach(bar => bar.classList.add('active'));

            audioElement!.play().catch(e => {
                console.warn('Playback failed:', e);
                radioStatus.textContent = '● ERROR';
                connectionStatus.textContent = 'ERROR';
                connectionStatus.style.color = '#ff0000';
            });
        });

        audioElement.addEventListener('error', () => {
            radioStatus.textContent = '● ERROR';
            connectionStatus.textContent = 'ERROR';
            connectionStatus.style.color = '#ff0000';
            eqBars.forEach(bar => bar.classList.remove('active'));
        });

        audioElement.src = currentStation.url_resolved;
        audioElement.load();
    }

    function stopRadio() {
        if (audioElement) {
            audioElement.pause();
            audioElement = null;
        }
        isPlaying = false;
        radioStatus.textContent = '● DETENIDO';
        connectionStatus.textContent = 'DESCONECTADO';
        connectionStatus.style.color = '#666';
        eqBars.forEach(bar => bar.classList.remove('active'));
    }

    // Event listeners
    playBtn.addEventListener('click', () => {
        playSound('click');
        if (currentStation && !isPlaying) {
            playStation();
        }
    });

    stopBtn.addEventListener('click', () => {
        playSound('click');
        stopRadio();
    });

    volumeSlider.addEventListener('input', () => {
        volume = parseInt(volumeSlider.value);
        volumeDisplay.textContent = volume + '%';
        if (audioElement) {
            audioElement.volume = volume / 100;
        }
    });

    categorySelect.addEventListener('change', () => {
        currentCategory = categorySelect.value;
        updateStationsList();
    });

    searchInput.addEventListener('input', () => {
        updateStationsList();
    });

    refreshBtn.addEventListener('click', () => {
        playSound('click');
        updateStationsList();
    });

    // Preset functionality
    presetBtns.forEach((btn, index) => {
        btn.addEventListener('click', () => {
            playSound('click');
            if (presetStations[index]) {
                selectStation(presetStations[index]);
            }
        });

        // Update preset button appearance
        if (presetStations[index]) {
            btn.style.background = '#4CAF50';
            btn.title = presetStations[index].name;
        }
    });

    savePresetBtn.addEventListener('click', () => {
        if (!currentStation) return;

        playSound('click');

        // Find first empty preset slot
        let slotIndex = presetStations.findIndex(preset => preset === null);
        if (slotIndex === -1) slotIndex = 0; // Overwrite first if all full

        presetStations[slotIndex] = currentStation;

        try {
            localStorage.setItem('winiaWavesPresets', JSON.stringify(presetStations));
        } catch (e) {
            console.warn('Could not save presets:', e);
        }

        // Update preset button
        const btn = presetBtns[slotIndex];
        btn.style.background = '#4CAF50';
        btn.title = currentStation.name;
    });

    // Initialize
    updateStationsList();

    return container;
}

// --- Pixel Dissolve Effect Implementation ---
function createPixelDissolveEffect() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pixelDissolve {
            0% {
                opacity: 1;
                filter: none;
            }
            25% {
                filter: pixelate(2px);
            }
            50% {
                filter: pixelate(4px);
                opacity: 0.7;
            }
            75% {
                filter: pixelate(8px);
                opacity: 0.3;
            }
            100% {
                filter: pixelate(16px);
                opacity: 0;
            }
        }

        @keyframes pixelDissolveIn {
            0% {
                filter: pixelate(16px);
                opacity: 0;
            }
            25% {
                filter: pixelate(8px);
                opacity: 0.3;
            }
            50% {
                filter: pixelate(4px);
                opacity: 0.7;
            }
            75% {
                filter: pixelate(2px);
            }
            100% {
                opacity: 1;
                filter: none;
            }
        }

        .pixel-dissolve-out {
            animation: pixelDissolve 1500ms ease-in-out forwards;
        }

        .pixel-dissolve-in {
            animation: pixelDissolveIn 1500ms ease-in-out forwards;
        }

        /* Fallback for browsers that don't support pixelate filter */
        @supports not (filter: pixelate(1px)) {
            @keyframes pixelDissolve {
                0% {
                    opacity: 1;
                    transform: scale(1);
                }
                25% {
                    opacity: 0.8;
                    transform: scale(0.98);
                    filter: blur(1px);
                }
                50% {
                    opacity: 0.6;
                    transform: scale(0.95);
                    filter: blur(2px);
                }
                75% {
                    opacity: 0.3;
                    transform: scale(0.9);
                    filter: blur(4px);
                }
                100% {
                    opacity: 0;
                    transform: scale(0.8);
                    filter: blur(8px);
                }
            }

            @keyframes pixelDissolveIn {
                0% {
                    opacity: 0;
                    transform: scale(0.8);
                    filter: blur(8px);
                }
                25% {
                    opacity: 0.3;
                    transform: scale(0.9);
                    filter: blur(4px);
                }
                50% {
                    opacity: 0.6;
                    transform: scale(0.95);
                    filter: blur(2px);
                }
                75% {
                    opacity: 0.8;
                    transform: scale(0.98);
                    filter: blur(1px);
                }
                100% {
                    opacity: 1;
                    transform: scale(1);
                    filter: none;
                }
            }
        }
    `;

    if (!document.head.querySelector('#pixel-dissolve-styles')) {
        style.id = 'pixel-dissolve-styles';
        document.head.appendChild(style);
    }
}

function applyPixelDissolveOut(element: HTMLElement, callback?: () => void) {
    createPixelDissolveEffect();
    element.classList.add('pixel-dissolve-out');

    setTimeout(() => {
        element.classList.remove('pixel-dissolve-out');
        if (callback) callback();
    }, 1500);
}

function applyPixelDissolveIn(element: HTMLElement, callback?: () => void) {
    createPixelDissolveEffect();
    element.classList.add('pixel-dissolve-in');

    setTimeout(() => {
        element.classList.remove('pixel-dissolve-in');
        if (callback) callback();
    }, 1500);
}

// Enhanced window opening with pixel dissolve effect
function openWindowWithPixelDissolve(appId: string, options?: any) {
    launchAppById(appId, options).then(() => {
        // Find the newly opened window
        const windows = document.querySelectorAll('.window');
        const newestWindow = windows[windows.length - 1] as HTMLElement;
        if (newestWindow) {
            newestWindow.style.opacity = '0';
            setTimeout(() => {
                applyPixelDissolveIn(newestWindow);
            }, 100);
        }
    });
}

// Enhanced window closing with pixel dissolve effect
function closeWindowWithPixelDissolve(windowId: string) {
    const windowEl = document.querySelector(`[data-window-id="${windowId}"]`) as HTMLElement;
    if (windowEl) {
        applyPixelDissolveOut(windowEl, () => {
            closeWindow(windowId);
        });
    }
}

// --- Winia Welcome Sequence ---
function showWiniaWelcomeSequence() {
    const welcomeOverlay = document.createElement('div');
    welcomeOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        z-index: 999999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: 'Segoe UI', sans-serif;
        opacity: 1;
        transition: opacity 1s ease-out;
    `;

    welcomeOverlay.innerHTML = `
        <div style="text-align: center; animation: fadeInUp 1s ease-out;">
            <div style="font-size: 72px; margin-bottom: 20px; animation: glow 2s ease-in-out infinite alternate;">
                🖥️
            </div>
            <h1 style="font-size: 48px; margin: 0 0 20px 0; font-weight: 300; animation: fadeInUp 1s ease-out 0.5s both;">
                Bienvenido a Winia
            </h1>
            <p style="font-size: 24px; margin: 0 0 40px 0; opacity: 0.9; animation: fadeInUp 1s ease-out 1s both;">
                Sistema Operativo Retro-Futurista
            </p>
            <div style="font-size: 16px; opacity: 0.7; animation: fadeInUp 1s ease-out 1.5s both;">
                Inicializando aplicaciones...
            </div>
            <div style="margin-top: 30px; animation: fadeInUp 1s ease-out 2s both;">
                <div style="width: 300px; height: 4px; background: rgba(255,255,255,0.2); border-radius: 2px; overflow: hidden;">
                    <div id="welcome-progress" style="width: 0%; height: 100%; background: linear-gradient(90deg, #00ff88, #00d4ff); border-radius: 2px; transition: width 0.3s ease;"></div>
                </div>
            </div>
        </div>
    `;

    // Add glow animation
    const glowStyle = document.createElement('style');
    glowStyle.textContent = `
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255,255,255,0.5); }
            to { text-shadow: 0 0 30px rgba(255,255,255,0.8), 0 0 40px rgba(0,255,136,0.3); }
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(glowStyle);

    document.body.appendChild(welcomeOverlay);

    // Animate progress bar
    const progressBar = welcomeOverlay.querySelector('#welcome-progress') as HTMLElement;
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5;
        if (progress >= 100) {
            progress = 100;
            clearInterval(progressInterval);

            // Start pixel dissolve sequence for desktop icons
            setTimeout(() => {
                startDesktopIconsPixelDissolve();

                // Fade out welcome screen
                setTimeout(() => {
                    welcomeOverlay.style.opacity = '0';
                    setTimeout(() => {
                        welcomeOverlay.remove();
                        glowStyle.remove();
                    }, 1000);
                }, 1000);
            }, 500);
        }
        progressBar.style.width = progress + '%';
    }, 200);
}

function startDesktopIconsPixelDissolve() {
    const desktopIcons = document.querySelectorAll('.desktop-icon');

    // Initially hide all icons
    desktopIcons.forEach(icon => {
        (icon as HTMLElement).style.opacity = '0';
    });

    // Animate icons appearing with pixel dissolve effect
    desktopIcons.forEach((icon, index) => {
        setTimeout(() => {
            applyPixelDissolveIn(icon as HTMLElement);
        }, index * 200); // Stagger the animations
    });

    // Also animate start menu if it's open
    if (!START_MENU_ELEMENT.classList.contains('hidden')) {
        const startMenuItems = START_MENU_ELEMENT.querySelectorAll('li');
        startMenuItems.forEach((item, index) => {
            setTimeout(() => {
                applyPixelDissolveIn(item as HTMLElement);
            }, (desktopIcons.length * 200) + (index * 100));
        });
    }
}

async function loadAndRenderAllAppsFromDB(desktopLayout?: DesktopIconState[]) {
    try {
        console.log("Starting loadAndRenderAllAppsFromDB...");
        START_MENU_APPS_LIST_ELEMENT.innerHTML = '';
        document.querySelectorAll('#winia-desktop .desktop-icon').forEach(el => el.remove());

        const apps = await getAllGeneratedApps();
        console.log("Apps loaded from DB:", apps.length, apps);

        if (apps.length === 0) {
            console.warn("No apps found in DB! This might indicate a problem with app initialization.");
            return;
        }

        apps.sort((a,b) => a.title.localeCompare(b.title)); // Sort apps alphabetically

        const layoutMap = new Map(desktopLayout?.map(item => [item.appId, item]));

        apps.forEach(app => {
            console.log("Creating desktop icon for:", app.title);
            const layout = layoutMap.get(app.id);
            createDesktopIcon(app, layout?.x, layout?.y);
            addAppToStartMenu(app);
        });
        console.log("Finished loadAndRenderAllAppsFromDB");
    } catch (error) {
        console.error("Error loading and rendering apps from DB:", error);
    }
}

// --- WiniaFS Path Helper Functions ---
function normalizePath(path: string): string {
    if (!path.startsWith('/')) path = '/' + path;
    if (path.length >= 3 && path[0] === '/' && path[2] === ':' && path[1].match(/[a-zA-Z]/)) {
         if (path.length === 3 || (path.length > 3 && path[3] !== '/')) {
            path = path.substring(0, 3) + '/' + path.substring(3);
        }
    }
    path = path.replace(/\/\//g, '/');
    if (path !== '/C:/' && path.endsWith('/') && path.length > 1) path = path.slice(0, -1);
    return path;
}

function getParentPath(path: string): string | null {
    const normPath = normalizePath(path);
    if (normPath === '/C:/') return null;
    const lastSlash = normPath.lastIndexOf('/');
    if (lastSlash <= 0) return '/C:/';
    let parent = normPath.substring(0, lastSlash);
    return normalizePath(parent === '/C:' ? '/C:/' : parent);
}

function getNodeName(path: string): string {
    const normPath = normalizePath(path);
    if (normPath === '/C:/') return 'C:';
    const lastSlash = normPath.lastIndexOf('/');
    return normPath.substring(lastSlash + 1);
}

// --- WiniaFS Core Functions ---
async function createFsNode(nodeData: {
    path: string;
    type: 'file' | 'folder';
    content?: string;
}): Promise<FSNode> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    const path = normalizePath(nodeData.path);
    const name = getNodeName(path);
    const now = Date.now();

    const newNode: FSNode = {
        path, name, type: nodeData.type,
        content: nodeData.content, createdAt: now, modifiedAt: now,
    };

    if (path !== '/C:/') {
        const parentPath = getParentPath(path);
        if (parentPath) {
            const parentNode = await getFsNode(parentPath);
            if (!parentNode || parentNode.type !== 'folder') {
                throw new Error(`Parent folder '${parentPath}' does not exist or is not a folder.`);
            }
        } else {
            throw new Error(`Cannot determine parent for path: ${path}`);
        }
    }

    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(FS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(FS_STORE_NAME);
        const request = store.add(newNode);
        request.onsuccess = () => resolve(newNode);
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getFsNode(path: string): Promise<FSNode | null> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    const normPath = normalizePath(path);
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(FS_STORE_NAME, "readonly");
        const store = transaction.objectStore(FS_STORE_NAME);
        const request = store.get(normPath);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getAllFsNodesFromDB(): Promise<FSNode[]> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(FS_STORE_NAME, "readonly");
        const store = transaction.objectStore(FS_STORE_NAME);
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}


async function fsPathExists(path: string): Promise<boolean> {
    const node = await getFsNode(path);
    return !!node;
}

async function updateFsNodeContent(path: string, newContent: string): Promise<FSNode | null> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    const normPath = normalizePath(path);
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(FS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(FS_STORE_NAME);
        const getRequest = store.get(normPath);

        getRequest.onsuccess = () => {
            const node = getRequest.result as FSNode | null;
            if (node && node.type === 'file') {
                node.content = newContent;
                node.modifiedAt = Date.now();
                const putRequest = store.put(node);
                putRequest.onsuccess = () => resolve(node);
                putRequest.onerror = (event) => reject((event.target as IDBRequest).error);
            } else {
                resolve(null);
            }
        };
        getRequest.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function listDirectoryContents(dirPath: string): Promise<FSNode[]> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    const normDirPath = normalizePath(dirPath);
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(FS_STORE_NAME, "readonly");
        const store = transaction.objectStore(FS_STORE_NAME);
        const children: FSNode[] = [];
        const lowerBound = normDirPath === '/C:/' ? '/C:/' : normDirPath + '/';
        const upperBound = normDirPath === '/C:/' ? '/C:/\uffff' : normDirPath + '/\uffff';

        const request = store.openCursor(IDBKeyRange.bound(lowerBound, upperBound, false, true));

        request.onsuccess = (event) => {
            const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
            if (cursor) {
                const nodePath = cursor.value.path as string;
                if (getParentPath(nodePath) === normDirPath) {
                    children.push(cursor.value);
                }
                cursor.continue();
            } else {
                resolve(children);
            }
        };
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function deleteFsNodeLogic(path: string, moveToRecycleBin: boolean = true): Promise<boolean> {
    if (!db) throw new Error("DB not initialized for FS operations.");
    const normPath = normalizePath(path);
    const node = await getFsNode(normPath);
    if (!node) return false;

    const nodesToDelete: FSNode[] = [node];
    if (node.type === 'folder') {
        async function getAllDescendants(folderPath: string, allNodes: FSNode[] = []): Promise<FSNode[]> {
            const directChildren = await listDirectoryContents(folderPath);
            for (const child of directChildren) {
                allNodes.push(child);
                if (child.type === 'folder') await getAllDescendants(child.path, allNodes);
            }
            return allNodes;
        }
        nodesToDelete.push(...await getAllDescendants(normPath));
    }
    nodesToDelete.sort((a, b) => b.path.length - a.path.length);

    return new Promise((resolve, reject) => {
        const storesToTransact = [FS_STORE_NAME];
        if (moveToRecycleBin) storesToTransact.push(RECYCLE_BIN_STORE_NAME);

        const transaction = db!.transaction(storesToTransact, "readwrite");
        let itemsSuccessfullyProcessedForBin = 0;

        transaction.oncomplete = () => {
            if (moveToRecycleBin && itemsSuccessfullyProcessedForBin > 0) updateRecycleBinDesktopIcon();
            resolve(true);
        };
        transaction.onerror = (event) => reject((event.target as IDBTransaction).error);
        transaction.onabort = (event) => reject(new Error("Transaction aborted"));

        const fsStore = transaction.objectStore(FS_STORE_NAME);
        const binStore = moveToRecycleBin ? transaction.objectStore(RECYCLE_BIN_STORE_NAME) : null;

        if (nodesToDelete.length === 0) { resolve(true); return; }

        nodesToDelete.forEach(nToDelete => {
            const deleteReq = fsStore.delete(nToDelete.path);
            deleteReq.onsuccess = () => {
                if (moveToRecycleBin && binStore) {
                    const binNode: FSNode = {
                        ...nToDelete, id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                        originalPath: nToDelete.path, modifiedAt: Date.now()
                    };
                    const addReq = binStore.add(binNode);
                    addReq.onsuccess = () => itemsSuccessfullyProcessedForBin++;
                    addReq.onerror = (e) => { if(!transaction.error) transaction.abort(); };
                }
            };
            deleteReq.onerror = (e) => { if(!transaction.error) transaction.abort(); };
        });
    });
}

// --- Recycle Bin Functions ---
async function getRecycleBinContents(): Promise<FSNode[]> {
    if (!db) throw new Error("DB not initialized.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readonly");
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function restoreFromRecycleBin(nodeId: string): Promise<boolean> {
    if (!db) throw new Error("DB not initialized.");
    return new Promise(async (resolve, reject) => {
        const transaction = db!.transaction([RECYCLE_BIN_STORE_NAME, FS_STORE_NAME], "readwrite");
        const binStore = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        const fsStore = transaction.objectStore(FS_STORE_NAME);
        let restoredSuccessfully = false;

        transaction.oncomplete = () => {
            if(restoredSuccessfully) updateRecycleBinDesktopIcon();
            resolve(restoredSuccessfully);
        };
        transaction.onerror = (e) => reject((e.target as IDBRequest).error);
        transaction.onabort = (e) => reject(new Error("Restore transaction aborted"));

        const getReq = binStore.get(nodeId);
        getReq.onsuccess = async () => {
            const nodeToRestore = getReq.result as FSNode | undefined;
            if (!nodeToRestore || !nodeToRestore.originalPath) { transaction.abort(); return; }

            const parentOriginalPath = getParentPath(nodeToRestore.originalPath);
            if (parentOriginalPath && parentOriginalPath !== '/C:/') {
                const parentNode = await getFsNode(parentOriginalPath);
                if (!parentNode || parentNode.type !== 'folder') {
                    console.warn(`Parent path ${parentOriginalPath} for restoring might not exist.`);
                }
            }

            let finalPath = nodeToRestore.originalPath;
            let counter = 0;
            while(await fsPathExists(finalPath)) {
                counter++;
                const nameOnly = getNodeName(nodeToRestore.originalPath!);
                const parentDir = getParentPath(nodeToRestore.originalPath!) || "/C:/";
                const extIndex = nameOnly.lastIndexOf('.');
                if (extIndex > 0 && extIndex < nameOnly.length -1) {
                    finalPath = normalizePath(`${parentDir}/${nameOnly.substring(0, extIndex)}_restored(${counter})${nameOnly.substring(extIndex)}`);
                } else {
                    finalPath = normalizePath(`${parentDir}/${nameOnly}_restored(${counter})`);
                }
            }

            const fsNode: FSNode = { ...nodeToRestore, path: finalPath, name: getNodeName(finalPath) };
            delete fsNode.id; delete fsNode.originalPath; fsNode.modifiedAt = Date.now();

            const addReq = fsStore.add(fsNode);
            addReq.onsuccess = () => {
                const deleteReq = binStore.delete(nodeId);
                deleteReq.onsuccess = () => restoredSuccessfully = true;
                deleteReq.onerror = () => transaction.abort();
            };
            addReq.onerror = () => transaction.abort();
        };
         getReq.onerror = () => transaction.abort();
    });
}

async function permanentlyDeleteFromRecycleBin(nodeId: string): Promise<boolean> {
    if (!db) throw new Error("DB not initialized.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readwrite");
        transaction.oncomplete = () => { updateRecycleBinDesktopIcon(); resolve(true);};
        transaction.onerror = (e) => reject((e.target as IDBRequest).error);
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        store.delete(nodeId);
    });
}

async function emptyRecycleBin(): Promise<void> {
    if (!db) throw new Error("DB not initialized.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readwrite");
        transaction.oncomplete = () => {
            playSound('empty_recycle_bin');
            updateRecycleBinDesktopIcon();
            resolve();
        };
        transaction.onerror = (e) => reject((e.target as IDBRequest).error);
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        store.clear();
    });
}

async function updateRecycleBinDesktopIcon() {
    const desktopIconImg = DESKTOP_ELEMENT.querySelector(`.desktop-icon[data-app-id="${recycleBinAppId}"] img`) as HTMLImageElement | null;
    const startMenuIconImg = START_MENU_APPS_LIST_ELEMENT.querySelector(`li[data-app-id="${recycleBinAppId}"] img`) as HTMLImageElement | null;

    try {
        const contents = await getRecycleBinContents();
        const newIcon = contents.length > 0 ? ICONS.RECYCLE_BIN_FULL : ICONS.RECYCLE_BIN_EMPTY;
        if (desktopIconImg) desktopIconImg.src = newIcon;
        if (startMenuIconImg) startMenuIconImg.src = newIcon;

        const appData = await getGeneratedApp(recycleBinAppId);
        if (appData && appData.iconDataUrl !== newIcon) {
            await saveGeneratedApp({ ...appData, iconDataUrl: newIcon });
        }

    } catch (error) {
        console.error("Error updating Recycle Bin desktop icon:", error);
    }
}

// --- Default File System Structure ---
async function ensureDefaultFileSystemStructure() {
    if (!db) {
        console.error("DB not initialized, cannot ensure default FS structure.");
        return;
    }
    try {
        const rootExists = await fsPathExists("/C:/");
        if (!rootExists) {
            await createFsNode({ path: "/C:/", type: "folder" });
            await createFsNode({ path: "/C:/My Documents", type: "folder" });
            await createFsNode({ path: "/C:/Program Files", type: "folder" });
            await createFsNode({ path: "/C:/Windows", type: "folder" });
            await createFsNode({
                path: "/C:/My Documents/Welcome.txt", type: "file",
                content: "Welcome to Winia!\n\nThis is your personal file system."
            });
        }
    } catch (error) {
        console.error("Error ensuring default file system structure:", error);
    }
}

// --- Recycle Bin UI ---
function launchRecycleBinWindow(appData: GeneratedApp) {
    createWindow(
        appData.id, appData.title,
        (win) => buildRecycleBinUI(win.id), // appData not needed here
        appData.iconDataUrl, '600px', '450px'
    );
}

function buildRecycleBinUI(windowId: string): HTMLElement {
    const container = document.createElement('div');
    container.className = 'recycle-bin-container';

    const toolbar = document.createElement('div');
    toolbar.className = 'recycle-bin-toolbar';
    const restoreBtn = document.createElement('button'); restoreBtn.className = 'cursor-hand'; restoreBtn.textContent = 'Restore Selected'; restoreBtn.disabled = true;
    const deleteBtn = document.createElement('button'); deleteBtn.className = 'cursor-hand'; deleteBtn.textContent = 'Delete Selected'; deleteBtn.disabled = true;
    const emptyBtn = document.createElement('button'); emptyBtn.className = 'cursor-hand'; emptyBtn.textContent = 'Empty Recycle Bin'; emptyBtn.disabled = true;
    toolbar.append(restoreBtn, deleteBtn, emptyBtn);

    const itemsView = document.createElement('div'); itemsView.className = 'recycle-bin-items-view';
    container.append(toolbar, itemsView);

    const toolbarElements = { restoreBtn, deleteBtn, emptyBtn };
    renderRecycleBinItems(itemsView, windowId, toolbarElements);

    restoreBtn.onclick = async () => {
        playSound('click');
        const selectedItem = itemsView.querySelector('.recycle-bin-item.selected') as HTMLElement;
        if (selectedItem?.dataset.nodeId && await restoreFromRecycleBin(selectedItem.dataset.nodeId)) {
            refreshRecycleBinView(windowId, itemsView, toolbarElements);
        } else if(selectedItem) {
            alert("Failed to restore item."); playSound('error');
        }
    };
    deleteBtn.onclick = async () => {
        playSound('click');
        const selectedItem = itemsView.querySelector('.recycle-bin-item.selected') as HTMLElement;
        if (selectedItem?.dataset.nodeId && window.confirm(`Permanently delete '${selectedItem.dataset.nodeName}'?`)) {
            await permanentlyDeleteFromRecycleBin(selectedItem.dataset.nodeId);
            refreshRecycleBinView(windowId, itemsView, toolbarElements);
        }
    };
    emptyBtn.onclick = async () => {
        playSound('click');
        if (window.confirm("Permanently delete all items in the Recycle Bin?")) {
            await emptyRecycleBin();
            refreshRecycleBinView(windowId, itemsView, toolbarElements);
        }
    };
    return container;
}

async function renderRecycleBinItems(
    itemsViewElement: HTMLElement, windowId: string,
    toolbar: { restoreBtn: HTMLButtonElement, deleteBtn: HTMLButtonElement, emptyBtn: HTMLButtonElement }
) {
    itemsViewElement.innerHTML = '';
    toolbar.restoreBtn.disabled = true; toolbar.deleteBtn.disabled = true;

    try {
        const items = await getRecycleBinContents();
        items.sort((a,b) => (b.modifiedAt || 0) - (a.modifiedAt || 0));
        toolbar.emptyBtn.disabled = items.length === 0;

        if (items.length === 0) {
            const p = document.createElement('p'); p.textContent = 'The Recycle Bin is empty.';
            p.style.textAlign = 'center'; p.style.padding = '20px';
            itemsViewElement.appendChild(p); return;
        }

        const header = document.createElement('div'); header.className = 'recycle-bin-item header';
        ['Name', 'Original Location', 'Date Deleted'].forEach(text => {
            const cell = document.createElement('div'); cell.textContent = text; header.appendChild(cell);
        });
        itemsViewElement.appendChild(header);

        items.forEach(item => {
            const itemEl = document.createElement('div'); itemEl.className = 'recycle-bin-item cursor-hand';
            itemEl.setAttribute('data-node-id', item.id!); itemEl.setAttribute('data-node-name', item.name);

            const nameCell = document.createElement('div');
            const icon = document.createElement('img');
            icon.src = item.type === 'folder' ? ICONS.FOLDER_CLOSED : ICONS.FILE_TEXT; icon.alt = item.type;
            nameCell.append(icon, document.createTextNode(item.name));

            const origPath = document.createElement('div'); origPath.textContent = item.originalPath || 'N/A';
            const dateDel = document.createElement('div'); dateDel.textContent = new Date(item.modifiedAt || item.createdAt).toLocaleString();
            itemEl.append(nameCell, origPath, dateDel);

            itemEl.addEventListener('click', () => {
                playSound('click');
                itemsViewElement.querySelector('.recycle-bin-item.selected')?.classList.remove('selected');
                itemEl.classList.add('selected');
                toolbar.restoreBtn.disabled = false; toolbar.deleteBtn.disabled = false;
            });
            itemsViewElement.appendChild(itemEl);
        });
    } catch (error) {
        itemsViewElement.textContent = `Error loading: ${(error as Error).message}`; playSound('error');
    }
}

function refreshRecycleBinView(
    windowId: string, itemsViewElement: HTMLElement,
    toolbar: { restoreBtn: HTMLButtonElement, deleteBtn: HTMLButtonElement, emptyBtn: HTMLButtonElement }
) {
    renderRecycleBinItems(itemsViewElement, windowId, toolbar);
}

// --- Control Panel ---
const PREDEFINED_WALLPAPERS = {
    'winia_teal': { name: 'Winia Teal', value: 'var(--winia-desktop-bg)', thumb: ICONS.WALLPAPER_WINIA_TEAL_THUMB, type: 'color' },
    'winia_hills': { name: 'Green Hills (Classic)', value: `url(${ICONS.WALLPAPER_HILLS_THUMB})`, thumb: ICONS.WALLPAPER_HILLS_THUMB, type: 'image' },
    'winia_abstract': { name: 'Winia Abstract', value: `url(${ICONS.WALLPAPER_WINIA_ABSTRACT_THUMB})`, thumb: ICONS.WALLPAPER_WINIA_ABSTRACT_THUMB, type: 'image' },
};
const DEFAULT_WALLPAPER_KEY = 'winia_teal';

function launchControlPanelWindow(appData: GeneratedApp) {
    const win = createWindow(
        appData.id, appData.title,
        (win) => buildControlPanelMainUI(win), // appData not needed here
        appData.iconDataUrl, '550px', '420px'
    );
    win.viewHistory = ['main'];
}

function buildControlPanelMainUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'control-panel-main-view';
    win.currentView = 'main';

    const categories = [
        { id: 'display', title: 'Display', icon: ICONS.DISPLAY_SETTINGS, action: () =>navigateToControlPanelView(win, 'display', buildDisplaySettingsUI) },
        { id: 'mouse', title: 'Mouse', icon: ICONS.MOUSE_SETTINGS, action: () => navigateToControlPanelView(win, 'mouse', buildMouseSettingsUI) },
        { id: 'sounds', title: 'Sounds', icon: ICONS.SOUND_SETTINGS, action: () => navigateToControlPanelView(win, 'sounds', buildSoundSettingsUI) },
        { id: 'codegenie', title: 'CodeGenie', icon: ICONS.CODEGENIE_SETTINGS, action: () => navigateToControlPanelView(win, 'codegenie', buildCodeGenieSettingsUI) },
        { id: 'system', title: 'System', icon: ICONS.SYSTEM_INFO, action: () => navigateToControlPanelView(win, 'system', buildSystemInfoUI) },
        { id: 'storage', title: 'Storage', icon: ICONS.STORAGE_SETTINGS, action: () => navigateToControlPanelView(win, 'storage', buildStorageSettingsUI) },
        { id: 'backup', title: 'Backup', icon: ICONS.BACKUP_SETTINGS, action: () => navigateToControlPanelView(win, 'backup', buildBackupSettingsUI) },
    ];

    categories.forEach(cat => {
        const button = document.createElement('button');
        button.className = 'control-panel-category-button cursor-hand';
        button.setAttribute('aria-label', cat.title);
        button.onclick = () => { playSound('click'); cat.action(); };
        button.onkeydown = (e: KeyboardEvent) => { if (e.key === 'Enter' || e.key === ' ') { playSound('click'); cat.action(); } };

        const img = document.createElement('img');
        img.src = cat.icon;
        img.alt = '';
        const span = document.createElement('span');
        span.textContent = cat.title;
        button.append(img, span);
        container.appendChild(button);
    });
    return container;
}

function navigateToControlPanelView(win: WiniaWindow, viewId: string, viewBuilder: (win: WiniaWindow) => HTMLElement) {
    const contentArea = win.element.querySelector('.winia-window-content');
    if (!contentArea) return;

    contentArea.innerHTML = '';
    const viewElement = viewBuilder(win);

    if (viewId !== 'main') {
        const backButton = document.createElement('button');
        backButton.textContent = '← Back';
        backButton.className = 'control-panel-back-button cursor-hand';
        backButton.onclick = () => {
            playSound('click');
            win.viewHistory?.pop();
            const previousViewId = win.viewHistory?.[win.viewHistory.length -1] || 'main';
            let previousViewBuilder = buildControlPanelMainUI;
            if (previousViewId === 'display') previousViewBuilder = buildDisplaySettingsUI;
            else if (previousViewId === 'mouse') previousViewBuilder = buildMouseSettingsUI;
            else if (previousViewId === 'sounds') previousViewBuilder = buildSoundSettingsUI;
            else if (previousViewId === 'codegenie') previousViewBuilder = buildCodeGenieSettingsUI;
            else if (previousViewId === 'system') previousViewBuilder = buildSystemInfoUI;
            else if (previousViewId === 'storage') previousViewBuilder = buildStorageSettingsUI;
            else if (previousViewId === 'codegenieAvatar') previousViewBuilder = buildCodeGenieAvatarSettingsUI;

            navigateToControlPanelView(win, previousViewId, previousViewBuilder);
        };
        viewElement.prepend(backButton);
    }

    contentArea.appendChild(viewElement);
    win.currentView = viewId;
    if (win.viewHistory && !win.viewHistory.includes(viewId) && viewId !== 'main') {
         win.viewHistory?.push(viewId);
    }
}

// --- Custom Wallpaper Store Functions ---
async function saveCustomWallpaper(id: string, dataUrl: string): Promise<void> {
    if (!db) throw new Error("DB not initialized for custom wallpapers.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(CUSTOM_WALLPAPERS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(CUSTOM_WALLPAPERS_STORE_NAME);
        const request = store.put({ id, dataUrl });
        request.onsuccess = () => resolve();
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function getCustomWallpaper(id: string): Promise<{id: string, dataUrl: string} | null> {
    if (!db) throw new Error("DB not initialized for custom wallpapers.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(CUSTOM_WALLPAPERS_STORE_NAME, "readonly");
        const store = transaction.objectStore(CUSTOM_WALLPAPERS_STORE_NAME);
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function applyCurrentWallpaper() {
    try {
        const currentWallpaperKey = await getWiniaSetting(SETTING_CURRENT_WALLPAPER) as string || DEFAULT_WALLPAPER_KEY;
        if (currentWallpaperKey === 'custom') {
            const customId = await getWiniaSetting(SETTING_CUSTOM_WALLPAPER_ID) as string;
            if (customId) {
                const customWallpaper = await getCustomWallpaper(customId);
                if (customWallpaper) {
                    DESKTOP_ELEMENT.style.backgroundImage = `url(${customWallpaper.dataUrl})`;
                    DESKTOP_ELEMENT.style.backgroundColor = '';
                    return;
                }
            }
        } else if (PREDEFINED_WALLPAPERS[currentWallpaperKey]) {
            const wp = PREDEFINED_WALLPAPERS[currentWallpaperKey];
            if (wp.type === 'image') {
                DESKTOP_ELEMENT.style.backgroundImage = wp.value;
                DESKTOP_ELEMENT.style.backgroundColor = '';
            } else {
                DESKTOP_ELEMENT.style.backgroundColor = wp.value.startsWith('var(') ? getComputedStyle(document.documentElement).getPropertyValue(wp.value.slice(4,-1).trim()) : wp.value;
                DESKTOP_ELEMENT.style.backgroundImage = '';
            }
            return;
        }
        DESKTOP_ELEMENT.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--winia-desktop-bg');
        DESKTOP_ELEMENT.style.backgroundImage = '';
    } catch (error) {
        console.error("Error applying wallpaper:", error);
         DESKTOP_ELEMENT.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--winia-desktop-bg');
         DESKTOP_ELEMENT.style.backgroundImage = '';
    }
}

// --- Display Settings UI ---
function buildDisplaySettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container';

    const wallpaperSection = document.createElement('div');
    wallpaperSection.className = 'settings-section';
    const wsTitle = document.createElement('div');
    wsTitle.className = 'settings-section-title';
    wsTitle.textContent = 'Fondo de Escritorio';
    wallpaperSection.appendChild(wsTitle);

    const previewContainer = document.createElement('div');
    previewContainer.className = 'wallpaper-preview-container';
    const currentPreview = document.createElement('div');
    currentPreview.className = 'current-wallpaper-preview';

    const wallpaperList = document.createElement('div');
    wallpaperList.className = 'wallpaper-list';

    const updateWallpaperListAndPreview = async () => {
        wallpaperList.innerHTML = '';
        const currentWallpaperKey = await getWiniaSetting(SETTING_CURRENT_WALLPAPER) as string || DEFAULT_WALLPAPER_KEY;
        let currentFullValue = '';

        Object.entries(PREDEFINED_WALLPAPERS).forEach(([key, wp]) => {
            const item = document.createElement('div');
            item.className = 'wallpaper-item cursor-hand';
            if (key === currentWallpaperKey) item.classList.add('selected');

            const thumb = document.createElement('div');
            thumb.className = 'wallpaper-item-thumbnail';
            if (wp.type === 'image') thumb.style.backgroundImage = wp.value;
            else thumb.style.backgroundColor = wp.value.startsWith('var(') ? getComputedStyle(document.documentElement).getPropertyValue(wp.value.slice(4,-1).trim()) : wp.value;

            item.append(thumb, document.createTextNode(wp.name));
            item.onclick = async () => {
                playSound('click');
                await setWiniaSetting(SETTING_CURRENT_WALLPAPER, key);
                await setWiniaSetting(SETTING_CUSTOM_WALLPAPER_ID, null);
                applyCurrentWallpaper();
                updateWallpaperListAndPreview();
            };
            wallpaperList.appendChild(item);
            if (key === currentWallpaperKey) currentFullValue = wp.type === 'image' ? wp.value : (wp.value.startsWith('var(') ? getComputedStyle(document.documentElement).getPropertyValue(wp.value.slice(4,-1).trim()) : wp.value) ;
        });

        if (currentWallpaperKey === 'custom') {
             const customId = await getWiniaSetting(SETTING_CUSTOM_WALLPAPER_ID) as string;
             if (customId) {
                const customWp = await getCustomWallpaper(customId);
                if (customWp) {
                    const item = document.createElement('div');
                    item.className = 'wallpaper-item selected cursor-hand';
                    const thumb = document.createElement('div');
                    thumb.className = 'wallpaper-item-thumbnail';
                    thumb.style.backgroundImage = `url(${customWp.dataUrl})`;
                    item.append(thumb, document.createTextNode('Personalizado'));
                    item.onclick = () => { playSound('click'); updateWallpaperListAndPreview(); };
                    wallpaperList.prepend(item);
                    currentFullValue = `url(${customWp.dataUrl})`;
                }
             }
        }

        if (currentFullValue.startsWith('url')) currentPreview.style.backgroundImage = currentFullValue;
        else currentPreview.style.backgroundColor = currentFullValue;
        currentPreview.style.backgroundSize = 'cover';
        currentPreview.style.backgroundPosition = 'center';

    };

    const uploadLabel = document.createElement('label');
    uploadLabel.textContent = 'Subir fondo personalizado:';
    uploadLabel.className = 'cursor-hand';
    const uploadInput = document.createElement('input');
    uploadInput.type = 'file';
    uploadInput.accept = 'image/*';
    uploadInput.className = 'cursor-hand';
    uploadInput.onchange = async (e) => {
        playSound('click');
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = async (event) => {
                const dataUrl = event.target?.result as string;
                const customId = `custom-${Date.now()}`;
                await saveCustomWallpaper(customId, dataUrl);
                await setWiniaSetting(SETTING_CURRENT_WALLPAPER, 'custom');
                await setWiniaSetting(SETTING_CUSTOM_WALLPAPER_ID, customId);
                applyCurrentWallpaper();
                updateWallpaperListAndPreview();
            };
            reader.readAsDataURL(file);
        }
    };

    previewContainer.append(currentPreview, wallpaperList);
    wallpaperSection.append(previewContainer, uploadLabel, uploadInput);

    const themeSection = document.createElement('div');
    themeSection.className = 'settings-section';
    const tsTitle = document.createElement('div'); tsTitle.className = 'settings-section-title'; tsTitle.textContent = 'Tema Visual';
    themeSection.appendChild(tsTitle);
    const themeLabel = document.createElement('label');
    themeLabel.textContent = 'Seleccionar tema:';
    const themeSelect = document.createElement('select');
    themeSelect.className = 'cursor-hand';
    ['Winia Clásico', 'Luna Azul (XP - Próximamente)'].forEach(themeName => {
        const option = document.createElement('option');
        option.value = themeName.split(' ')[0].toLowerCase();
        option.textContent = themeName;
        if (themeName.includes('Próximamente')) option.disabled = true;
        themeSelect.appendChild(option);
    });
    themeSelect.value = 'winia';
    themeSelect.onchange = () => playSound('click');
    themeSection.append(themeLabel, themeSelect);

    const screensaverSection = document.createElement('div');
    screensaverSection.className = 'settings-section';
    const ssTitle = document.createElement('div'); ssTitle.className = 'settings-section-title'; ssTitle.textContent = 'Protector de Pantalla';
    screensaverSection.appendChild(ssTitle);

    const ssTypeLabel = document.createElement('label'); ssTypeLabel.textContent = 'Tipo:';
    const ssTypeSelect = document.createElement('select');
    ssTypeSelect.className = 'cursor-hand';
    const ssTypes = { none: 'Ninguno', floatingText: 'Texto Flotante (Winia)', stars: 'Estrellas Viajeras' };
    Object.entries(ssTypes).forEach(([key, name]) => {
        const option = document.createElement('option'); option.value = key; option.textContent = name;
        ssTypeSelect.appendChild(option);
    });

    const ssTextLabel = document.createElement('label'); ssTextLabel.textContent = 'Texto (para Texto Flotante):';
    const ssTextInput = document.createElement('input'); ssTextInput.type = 'text';
    ssTextInput.maxLength = 50;
    ssTextInput.className = 'cursor-text';

    ssTypeSelect.onchange = () => {
        playSound('click');
        ssTextLabel.style.display = ssTypeSelect.value === 'floatingText' ? 'block' : 'none';
        ssTextInput.style.display = ssTypeSelect.value === 'floatingText' ? 'block' : 'none';
    };

    const ssPreviewButton = document.createElement('button'); ssPreviewButton.textContent = 'Vista Previa';
    ssPreviewButton.className = 'cursor-hand';
    ssPreviewButton.onclick = async () => {
        playSound('click');
        const type = ssTypeSelect.value;
        const text = ssTextInput.value || "Winia";
        await setWiniaSetting(SETTING_SCREENSAVER_TYPE, type);
        if (type === 'floatingText') await setWiniaSetting(SETTING_SCREENSAVER_TEXT, text);
        previewScreensaver(type, text);
    };

    const ssApplyButton = document.createElement('button');
    ssApplyButton.textContent = "Aplicar";
    ssApplyButton.className = 'cursor-hand';
    ssApplyButton.onclick = async () => {
        playSound('click');
        await setWiniaSetting(SETTING_SCREENSAVER_TYPE, ssTypeSelect.value);
        if (ssTypeSelect.value === 'floatingText') await setWiniaSetting(SETTING_SCREENSAVER_TEXT, ssTextInput.value);
        alert("Configuración del protector de pantalla guardada.");
    };

    screensaverSection.append(ssTypeLabel, ssTypeSelect, ssTextLabel, ssTextInput, ssPreviewButton, ssApplyButton);

    Promise.all([
        getWiniaSetting(SETTING_SCREENSAVER_TYPE),
        getWiniaSetting(SETTING_SCREENSAVER_TEXT)
    ]).then(([type, text]) => {
        ssTypeSelect.value = type as string || 'none';
        ssTextInput.value = text as string || "Winia";
        ssTypeSelect.dispatchEvent(new Event('change'));
    });

    updateWallpaperListAndPreview();
    container.append(wallpaperSection, themeSection, screensaverSection);
    return container;
}

let screensaverActive = false;
let starsAnimationId: number;

function previewScreensaver(type: string, text: string) {
    if (screensaverActive) return;
    if (type === 'none') {
        alert("Ningún protector de pantalla seleccionado para previsualizar.");
        playSound('click');
        return;
    }
    screensaverActive = true;
    SCREENSAVER_OVERLAY_ELEMENT.innerHTML = '';
    SCREENSAVER_OVERLAY_ELEMENT.classList.remove('hidden');
    SCREENSAVER_OVERLAY_ELEMENT.classList.add('cursor-none'); // Hide system cursor

    if (type === 'floatingText') {
        const textEl = document.createElement('div');
        textEl.className = 'screensaver-floating-text';
        textEl.textContent = text;
        SCREENSAVER_OVERLAY_ELEMENT.appendChild(textEl);
        let x = Math.random() * (SCREENSAVER_OVERLAY_ELEMENT.clientWidth - textEl.clientWidth);
        let y = Math.random() * (SCREENSAVER_OVERLAY_ELEMENT.clientHeight - textEl.clientHeight);
        let dx = (Math.random() < 0.5 ? -1 : 1) * (Math.random() * 2 + 1);
        let dy = (Math.random() < 0.5 ? -1 : 1) * (Math.random() * 2 + 1);
        function animateFloatingText() {
            if (!screensaverActive) return;
            x += dx; y += dy;
            if (x <= 0 || x + textEl.clientWidth >= SCREENSAVER_OVERLAY_ELEMENT.clientWidth) dx *= -1;
            if (y <= 0 || y + textEl.clientHeight >= SCREENSAVER_OVERLAY_ELEMENT.clientHeight) dy *= -1;
            textEl.style.left = `${x}px`;
            textEl.style.top = `${y}px`;
            requestAnimationFrame(animateFloatingText);
        }
        animateFloatingText();
    } else if (type === 'stars') {
        const canvas = document.createElement('canvas');
        canvas.className = 'screensaver-stars-canvas';
        SCREENSAVER_OVERLAY_ELEMENT.appendChild(canvas);
        const ctx = canvas.getContext('2d')!;
        canvas.width = SCREENSAVER_OVERLAY_ELEMENT.clientWidth;
        canvas.height = SCREENSAVER_OVERLAY_ELEMENT.clientHeight;
        const stars: {x:number, y:number, z:number, size:number}[] = [];
        for(let i=0; i<200; i++) {
            stars.push({
                x: Math.random() * canvas.width - canvas.width/2,
                y: Math.random() * canvas.height - canvas.height/2,
                z: Math.random() * canvas.width,
                size: Math.random() * 2 + 0.5
            });
        }
        function drawStars() {
            if (!screensaverActive) { cancelAnimationFrame(starsAnimationId); return;}
            ctx.fillStyle = 'black';
            ctx.fillRect(0,0,canvas.width, canvas.height);
            ctx.fillStyle = 'white';
            ctx.save();
            ctx.translate(canvas.width/2, canvas.height/2);
            for(const star of stars) {
                star.z -= 2;
                if(star.z <= 0) star.z = canvas.width;
                const x = (star.x / star.z) * canvas.width;
                const y = (star.y / star.z) * canvas.width;
                const size = (star.size / star.z) * canvas.width;
                if (x > -canvas.width/2 && x < canvas.width/2 && y > -canvas.height/2 && y < canvas.height/2) {
                    ctx.fillRect(x, y, size, size);
                }
            }
            ctx.restore();
            starsAnimationId = requestAnimationFrame(drawStars);
        }
        drawStars();
    }

    const stopScreensaver = () => {
        screensaverActive = false;
        SCREENSAVER_OVERLAY_ELEMENT.classList.add('hidden');
        SCREENSAVER_OVERLAY_ELEMENT.innerHTML = '';
        SCREENSAVER_OVERLAY_ELEMENT.classList.remove('cursor-none');
        window.removeEventListener('click', stopScreensaver);
        window.removeEventListener('keydown', stopScreensaver);
    };
    window.addEventListener('click', stopScreensaver, { once: true });
    window.addEventListener('keydown', stopScreensaver, { once: true });
}


// --- Sound Settings UI ---
function buildSoundSettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container';
    const title = document.createElement('div'); title.className = 'settings-section-title'; title.textContent = 'Configuración de Sonido';
    container.appendChild(title);

    const createCheckboxSetting = (labelText: string, settingKey: string, currentValue: boolean, onChange: (value: boolean) => void) => {
        const row = document.createElement('div');
        row.className = 'settings-control-row';
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `setting-${settingKey}`;
        checkbox.checked = currentValue;
        checkbox.className = 'cursor-hand';
        checkbox.onchange = () => { playSound('click'); onChange(checkbox.checked); };
        const label = document.createElement('label');
        label.htmlFor = checkbox.id;
        label.textContent = labelText;
        label.className = 'cursor-hand';
        row.append(checkbox, label);
        return row;
    };

    const volumeControlRow = document.createElement('div');
    volumeControlRow.className = 'settings-control-row';
    const volumeLabel = document.createElement('label');
    volumeLabel.htmlFor = 'setting-masterVolume';
    volumeLabel.textContent = 'Volumen Maestro:';
    const volumeSlider = document.createElement('input');
    volumeSlider.type = 'range';
    volumeSlider.id = 'setting-masterVolume';
    volumeSlider.min = '0';
    volumeSlider.max = '1';
    volumeSlider.step = '0.01';
    volumeSlider.value = winiaMasterVolume.toString();
    volumeSlider.className = 'cursor-hand';
    const volumeValueDisplay = document.createElement('span');
    volumeValueDisplay.textContent = `${Math.round(winiaMasterVolume * 100)}%`;
    volumeValueDisplay.style.minWidth = '40px';
    volumeValueDisplay.style.textAlign = 'right';


    volumeSlider.oninput = () => {
        const newVolume = parseFloat(volumeSlider.value);
        updateMasterVolume(newVolume);
        volumeValueDisplay.textContent = `${Math.round(newVolume * 100)}%`;
    };
    volumeSlider.onchange = () => {
        playSound('click');
    };

    volumeControlRow.append(volumeLabel, volumeSlider, volumeValueDisplay);
    container.appendChild(volumeControlRow);

    container.appendChild(createCheckboxSetting('Activar sonidos del sistema', SETTING_SYSTEM_SOUNDS_ENABLED, systemSoundsEnabled, async (val) => {
        systemSoundsEnabled = val;
        await setWiniaSetting(SETTING_SYSTEM_SOUNDS_ENABLED, val);
    }));
    container.appendChild(createCheckboxSetting('Activar música ambiental (simulada)', SETTING_AMBIENT_MUSIC_ENABLED, ambientMusicEnabled, async (val) => {
        ambientMusicEnabled = val;
        await setWiniaSetting(SETTING_AMBIENT_MUSIC_ENABLED, val);
        if (val) console.log("[SOUND] Placeholder: Ambient music would start now.");
        else console.log("[SOUND] Placeholder: Ambient music would stop now.");
    }));

    return container;
}

// --- Custom Cursor DB Functions ---
async function saveCustomCursor(cursorName: string, dataUrl: string): Promise<void> {
    if (!db) throw new Error("DB not initialized for custom cursors.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_CUSTOM_CURSORS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(WINIA_CUSTOM_CURSORS_STORE_NAME);
        const request = store.put({ cursorName, dataUrl });
        request.onsuccess = () => resolve();
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function getCustomCursor(cursorName: string): Promise<{cursorName: string, dataUrl: string} | null> {
    if (!db) return null;
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_CUSTOM_CURSORS_STORE_NAME, "readonly");
        const store = transaction.objectStore(WINIA_CUSTOM_CURSORS_STORE_NAME);
        const request = store.get(cursorName);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function getAllCustomCursorsFromDB(): Promise<{cursorName: string, dataUrl: string}[]> {
    if (!db) return [];
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_CUSTOM_CURSORS_STORE_NAME, "readonly");
        const store = transaction.objectStore(WINIA_CUSTOM_CURSORS_STORE_NAME);
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function clearAllCustomCursors(): Promise<void> {
    if (!db) throw new Error("DB not initialized for custom cursors.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(WINIA_CUSTOM_CURSORS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(WINIA_CUSTOM_CURSORS_STORE_NAME);
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

// --- Cursor Styling Application ---
function applyCustomCursorStyle(cursorKey: string, dataUrl: string) {
    let styleSheet = document.getElementById('winia-custom-cursors-style') as HTMLStyleElement | null;
    if (!styleSheet) {
        styleSheet = document.createElement('style');
        styleSheet.id = 'winia-custom-cursors-style';
        document.head.appendChild(styleSheet);
    }

    const className = CURSOR_CLASS_MAP[cursorKey];
    const hotspot = CURSOR_HOTSPOTS[cursorKey] || { x: 0, y: 0 };
    const newRule = `.${className} { cursor: url('${dataUrl}') ${hotspot.x} ${hotspot.y}, auto !important; }`;

    // For body-level cursor changes during drag/resize
    const bodyClassName = `cursor-${cursorKey.toLowerCase().replace(/_/g, '-')}-global`; // e.g. cursor-move-global
    const bodyRule = `.${bodyClassName}, .${bodyClassName} * { cursor: url('${dataUrl}') ${hotspot.x} ${hotspot.y}, auto !important; }`;


    let updatedCssText = "";
    const rulesToRemove: string[] = [];
    if(styleSheet.sheet){
        for (let i = 0; i < styleSheet.sheet.cssRules.length; i++) {
            const rule = styleSheet.sheet.cssRules[i] as CSSStyleRule;
            if (rule.selectorText === `.${className}` || rule.selectorText?.startsWith(`.${bodyClassName}`)) {
                 // Will be replaced or re-added
            } else {
                updatedCssText += rule.cssText + "\n";
            }
        }
    }


    updatedCssText += newRule + "\n";
    if (className.startsWith('cursor-move') || className.startsWith('cursor-resize') || className.startsWith('cursor-wait') || className.startsWith('cursor-grabbing')) {
         updatedCssText += bodyRule + "\n";
    }


    styleSheet.textContent = updatedCssText;

    // Update body cursor if it's the default one being changed
    if (cursorKey === 'DEFAULT') {
        document.body.style.cursor = `url('${dataUrl}') ${hotspot.x} ${hotspot.y}, auto`;
    }
}

async function applyAllCustomCursorsFromDB() {
    const customCursors = await getAllCustomCursorsFromDB();
    const customCursorMap = new Map(customCursors.map(c => [c.cursorName, c.dataUrl]));

    Object.keys(CURSORS).forEach(cursorKey => {
        const dataUrl = customCursorMap.get(cursorKey) || CURSORS[cursorKey];
        applyCustomCursorStyle(cursorKey, dataUrl);
    });
}


// --- Mouse Settings UI (Control Panel) ---
function buildMouseSettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container mouse-settings-view';
    const title = document.createElement('div');
    title.className = 'settings-section-title';
    title.textContent = 'Apariencia del Puntero del Ratón';
    container.appendChild(title);

    const description = document.createElement('p');
    description.textContent = 'Personaliza los punteros del ratón subiendo archivos PNG (se recomienda 32x32px).';
    container.appendChild(description);

    const cursorGrid = document.createElement('div');
    cursorGrid.className = 'mouse-settings-grid';

    const cursorTypesToDisplay = [
        { key: 'DEFAULT', name: 'Normal (Flecha)' },
        { key: 'HAND', name: 'Mano (Enlace)' },
        { key: 'TEXT', name: 'Texto (I-Beam)' },
        { key: 'MOVE', name: 'Mover' },
        { key: 'WAIT', name: 'Espera (Reloj de Arena)' },
        { key: 'RESIZE_NS', name: 'Redimensionar Vertical (N-S)' },
        { key: 'RESIZE_EW', name: 'Redimensionar Horizontal (E-O)' },
        { key: 'RESIZE_NWSE', name: 'Redimensionar Diagonal (NO-SE)' },
        { key: 'RESIZE_NESW', name: 'Redimensionar Diagonal (NE-SO)' },
    ];


    cursorTypesToDisplay.forEach(cursorType => {
        const item = document.createElement('div');
        item.className = 'mouse-cursor-item';

        const stateLabel = document.createElement('div');
        stateLabel.className = 'mouse-cursor-label';
        stateLabel.textContent = cursorType.name;

        const previewWrapper = document.createElement('div');
        previewWrapper.className = 'mouse-cursor-preview-wrapper';
        const previewImg = document.createElement('img');
        previewImg.className = 'mouse-cursor-preview';
        previewImg.alt = `${cursorType.name} preview`;

        getCustomCursor(cursorType.key).then(customCursor => {
            previewImg.src = customCursor?.dataUrl || CURSORS[cursorType.key];
        }).catch(() => {
            previewImg.src = CURSORS[cursorType.key];
        });
        previewWrapper.appendChild(previewImg);


        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/png';
        fileInput.className = 'mouse-cursor-input cursor-hand';
        fileInput.id = `cursor-input-${cursorType.key}`;

        const fileInputLabel = document.createElement('label');
        fileInputLabel.htmlFor = fileInput.id;
        fileInputLabel.textContent = "Cambiar...";
        fileInputLabel.className = "winia-button-classic cursor-hand";
        fileInputLabel.style.padding = "2px 5px";
        fileInputLabel.style.fontSize = "10px";


        fileInput.style.display = 'none'; // Hide actual input

        fileInput.onchange = async (e) => {
            playSound('click');
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file && file.type === "image/png") {
                const reader = new FileReader();
                reader.onload = async (event) => {
                    const dataUrl = event.target?.result as string;
                    await saveCustomCursor(cursorType.key, dataUrl);
                    applyCustomCursorStyle(cursorType.key, dataUrl);
                    previewImg.src = dataUrl;
                };
                reader.readAsDataURL(file);
            } else if (file) {
                alert("Por favor, sube un archivo PNG.");
                playSound('error');
            }
        };
        item.append(stateLabel, previewWrapper, fileInput, fileInputLabel);
        cursorGrid.appendChild(item);
    });

    container.appendChild(cursorGrid);

    const resetButton = document.createElement('button');
    resetButton.textContent = 'Restablecer Todos los Punteros a Predeterminados';
    resetButton.className = 'winia-button-classic cursor-hand';
    resetButton.style.marginTop = '15px';
    resetButton.onclick = async () => {
        playSound('click');
        if (window.confirm("¿Estás seguro de que quieres restablecer todos los punteros del ratón a los predeterminados?")) {
            await clearAllCustomCursors();
            await applyAllCustomCursorsFromDB(); // This will re-apply defaults
            // Refresh the view
            const currentPreviews = container.querySelectorAll('.mouse-cursor-preview') as NodeListOf<HTMLImageElement>;
             currentPreviews.forEach(img => {
                const key = (img.parentElement?.parentElement?.querySelector('input[type="file"]') as HTMLInputElement)?.id.replace('cursor-input-','');
                if (key && CURSORS[key]) {
                    img.src = CURSORS[key];
                }
            });
            alert("Punteros restablecidos a predeterminados.");
        }
    };
    container.appendChild(resetButton);
    return container;
}



// --- CodeGenie Settings UI & Custom Sprites ---
async function saveCustomCodeGenieSprite(stateName: string, dataUrl: string): Promise<void> {
    if (!db) throw new Error("DB not initialized for CodeGenie sprites.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(CUSTOM_CODEGENIE_SPRITES_STORE_NAME, "readwrite");
        const store = transaction.objectStore(CUSTOM_CODEGENIE_SPRITES_STORE_NAME);
        const request = store.put({ stateName, dataUrl });
        request.onsuccess = () => resolve();
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function getCustomCodeGenieSprite(stateName: string): Promise<{stateName: string, dataUrl: string} | null> {
    if (!db) return null; // Gracefully handle no DB
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(CUSTOM_CODEGENIE_SPRITES_STORE_NAME, "readonly");
        const store = transaction.objectStore(CUSTOM_CODEGENIE_SPRITES_STORE_NAME);
        const request = store.get(stateName);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

async function clearAllCustomCodeGenieSprites(): Promise<void> {
    if (!db) throw new Error("DB not initialized for CodeGenie sprites.");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(CUSTOM_CODEGENIE_SPRITES_STORE_NAME, "readwrite");
        const store = transaction.objectStore(CUSTOM_CODEGENIE_SPRITES_STORE_NAME);
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = (e) => reject((e.target as IDBRequest).error);
    });
}

function buildCodeGenieSettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container';
    const title = document.createElement('div'); title.className = 'settings-section-title'; title.textContent = 'Configuración de CodeGenie';
    container.appendChild(title);

    // Toggle Avatar Visibility
    const row = document.createElement('div'); row.className = 'settings-control-row';
    const checkbox = document.createElement('input'); checkbox.type = 'checkbox';
    checkbox.id = 'setting-showCodeGenieAvatar';
    checkbox.className = 'cursor-hand';

    const label = document.createElement('label');
    label.htmlFor = checkbox.id;
    label.textContent = 'Mostrar avatar de CodeGenie en el escritorio';
    label.className = 'cursor-hand';

    getWiniaSetting(SETTING_SHOW_CODEGENIE_AVATAR).then(val => {
        checkbox.checked = val !== false; // Default to true if setting is null/undefined
        CODEGENIE_CHARACTER_HOST_ELEMENT.classList.toggle('hidden', !checkbox.checked && !isCodeGenieAssistantActive);
    });

    checkbox.onchange = async () => {
        playSound('click');
        const showAvatar = checkbox.checked;
        await setWiniaSetting(SETTING_SHOW_CODEGENIE_AVATAR, showAvatar);
        CODEGENIE_CHARACTER_HOST_ELEMENT.classList.toggle('hidden', !showAvatar && !isCodeGenieAssistantActive);
    };
    row.append(checkbox, label);
    container.appendChild(row);

    // Button to go to Avatar Customization
    const customizeAvatarButton = document.createElement('button');
    customizeAvatarButton.textContent = 'Personalizar Sprites del Avatar...';
    customizeAvatarButton.className = 'cursor-hand';
    customizeAvatarButton.style.marginTop = '10px';
    customizeAvatarButton.onclick = () => {
        playSound('click');
        navigateToControlPanelView(win, 'codegenieAvatar', buildCodeGenieAvatarSettingsUI);
    };
    container.appendChild(customizeAvatarButton);

    return container;
}

function buildCodeGenieAvatarSettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container codegenie-avatar-settings';
    const title = document.createElement('div');
    title.className = 'settings-section-title';
    title.textContent = 'Personalizar Sprites del Avatar de CodeGenie';
    container.appendChild(title);

    const description = document.createElement('p');
    description.textContent = 'Sube imágenes PNG (idealmente 64x64px con fondo transparente) para cada estado del avatar.';
    container.appendChild(description);

    const spriteGrid = document.createElement('div');
    spriteGrid.className = 'codegenie-sprite-grid';

    // Use CodeGenieAvatarState enum keys for iteration
    Object.keys(CodeGenieAvatarState).forEach(stateKey => {
        const stateName = stateKey as keyof typeof CodeGenieAvatarState; // e.g., "IDLE"
        const stateEnumValue = CodeGenieAvatarState[stateName]; // e.g., CodeGenieAvatarState.IDLE for display

        const item = document.createElement('div');
        item.className = 'codegenie-sprite-item';

        const stateLabel = document.createElement('div');
        stateLabel.className = 'codegenie-sprite-state-label';
        stateLabel.textContent = stateEnumValue.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()); // Format for display

        const previewImg = document.createElement('img');
        previewImg.className = 'codegenie-sprite-preview';
        previewImg.alt = `${stateEnumValue} preview`;
        // Load current sprite (custom or default)
        getCustomCodeGenieSprite(stateName).then(customSprite => {
            if (customSprite?.dataUrl) {
                previewImg.src = customSprite.dataUrl;
            } else {
                previewImg.src = CODEGENIE_AVATAR_SPRITES[stateName] || CODEGENIE_AVATAR_SPRITES.IDLE;
            }
        });

        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/png';
        fileInput.className = 'codegenie-sprite-input cursor-hand';
        fileInput.onchange = async (e) => {
            playSound('click');
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = async (event) => {
                    const dataUrl = event.target?.result as string;
                    await saveCustomCodeGenieSprite(stateName, dataUrl);
                    previewImg.src = dataUrl;
                    if (currentCodeGenieState === stateEnumValue) { // If current avatar is this state, update it live
                        updateCodeGenieAvatar(currentCodeGenieState);
                    }
                };
                reader.readAsDataURL(file);
            }
        };
        item.append(stateLabel, previewImg, fileInput);
        spriteGrid.appendChild(item);
    });

    container.appendChild(spriteGrid);

    const resetButton = document.createElement('button');
    resetButton.textContent = 'Restablecer Todos los Sprites a Predeterminados';
    resetButton.className = 'cursor-hand';
    resetButton.style.marginTop = '15px';
    resetButton.onclick = async () => {
        playSound('click');
        if (window.confirm("¿Estás seguro de que quieres restablecer todos los sprites del avatar a los predeterminados?")) {
            await clearAllCustomCodeGenieSprites();
            // Refresh the view to show default sprites
            navigateToControlPanelView(win, 'codegenieAvatar', buildCodeGenieAvatarSettingsUI);
            updateCodeGenieAvatar(currentCodeGenieState); // Update current avatar if visible
        }
    };
    container.appendChild(resetButton);
    return container;
}


// --- System Info UI (Placeholder) ---
function buildSystemInfoUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container';
    const title = document.createElement('div'); title.className = 'settings-section-title'; title.textContent = 'Información del Sistema (Simulada)';
    container.appendChild(title);

    const infoList = document.createElement('ul');
    const infos = {
        "Sistema Operativo": "Winia Classic Edition",
        "Versión": "1.0 (CodeGenie Release)",
        "Procesador Simulado": "WiniaCPU @ 1.0 WHz",
        "RAM Instalada (Simulada)": "64 MB (Nostalgia Pura)",
        "Espacio en Disco C: (Simulado)": "Calculando..."
    };
    Object.entries(infos).forEach(([key, value]) => {
        const li = document.createElement('li');
        li.innerHTML = `<strong>${key}:</strong> ${value}`;
        infoList.appendChild(li);
    });
    container.appendChild(infoList);
    return container;
}

// --- Storage Settings UI (Basic) ---
function buildStorageSettingsUI(win: WiniaWindow): HTMLElement {
    const container = document.createElement('div');
    container.className = 'settings-view-container';
    const title = document.createElement('div'); title.className = 'settings-section-title'; title.textContent = 'Almacenamiento Winia';
    container.appendChild(title);

    const resetButton = document.createElement('button');
    resetButton.textContent = 'Restablecer Configuración de Pantalla';
    resetButton.className = 'cursor-hand';
    resetButton.onclick = async () => {
        playSound('click');
        if (window.confirm("¿Restablecer fondo de pantalla y protector a los predeterminados?")) {
            await deleteWiniaSetting(SETTING_CURRENT_WALLPAPER);
            await deleteWiniaSetting(SETTING_CUSTOM_WALLPAPER_ID);
            await deleteWiniaSetting(SETTING_SCREENSAVER_TYPE);
            await deleteWiniaSetting(SETTING_SCREENSAVER_TEXT);
            await applyCurrentWallpaper();
            if (win.currentView === 'display' && win.element.querySelector('.settings-view-container')) {
                navigateToControlPanelView(win, 'display', buildDisplaySettingsUI);
            }
            alert("Configuración de pantalla restablecida.");
        }
    };
    container.appendChild(resetButton);

    const soonText = document.createElement('p');
    soonText.textContent = "Más opciones de gestión de almacenamiento (limpiar caché de apps, resetear Winia) estarán disponibles pronto.";
    soonText.style.marginTop = "15px";
    soonText.style.fontStyle = "italic";
    container.appendChild(soonText);

    return container;
}

// --- Winia Web Voyager (Browser) ---
const VOYAGER_HOME_URL = 'https://oldgoogle.neocities.org/2013/';


function launchWebVoyagerWindow(appData: GeneratedApp): WiniaWindow {
    return createWindow(
        appData.id,
        appData.title,
        (win, data) => createEnhancedWebVoyagerContent(win, data),
        appData.iconDataUrl,
        '700px', '500px',
        appData // Pass appData to createWindow for the contentGenerator
    );
}

function updateVoyagerDisplayState(
    win: WiniaWindow,
    statusMessage: string,
    state: 'loading' | 'done' | 'error' | 'stopped' | 'ready',
    errorUrl?: string
) {
    if (!win.voyagerStatusBar || !win.voyagerLoadingIndicator || !win.voyagerErrorDisplay || !win.voyagerIframe || !win.voyagerStopButton) {
        return;
    }

    win.voyagerStatusBar.textContent = statusMessage;
    win.voyagerLoadingIndicator.classList.add('hidden');
    win.voyagerErrorDisplay.classList.add('hidden');
    win.voyagerIframe.style.display = 'none'; // Default to hiding iframe
    win.voyagerStopButton.disabled = true;

    // Update Go menu items (Back/Forward)
    const backMenuItem = win.voyagerMenuBar?.querySelector('[data-action="go-back"]') as HTMLDivElement | null;
    const forwardMenuItem = win.voyagerMenuBar?.querySelector('[data-action="go-forward"]') as HTMLDivElement | null;

    let canGoBack = false;
    let canGoForward = false;
    try {
        if (win.voyagerIframe.contentWindow && win.voyagerIframe.contentWindow.history.length > 1) {
            // This is a simplification. True canGoBack/Forward requires knowing current history index.
            // For now, enable if history.length > 1.
            canGoBack = true; // Assuming if length > 1, back is possible.
            // canGoForward logic is harder without current index.
        }
    } catch (e) { /* Cross-origin access can fail */ }

    if (backMenuItem) backMenuItem.classList.toggle('disabled', !canGoBack);
    if (forwardMenuItem) forwardMenuItem.classList.toggle('disabled', !canGoForward); // This will likely always be enabled if back is.


    switch (state) {
        case 'loading':
            win.voyagerLoadingIndicator.classList.remove('hidden');
            win.voyagerStopButton.disabled = false;
            break;
        case 'done':
            win.voyagerIframe.style.display = 'block';
            break;
        case 'error':
            win.voyagerErrorDisplay.textContent = errorUrl ? `Error: Could not load ${errorUrl}.` : statusMessage;
            win.voyagerErrorDisplay.classList.remove('hidden');
            playSound('error');
            break;
        case 'stopped':
            win.voyagerIframe.style.display = 'block'; // Show potentially partially loaded content
            break;
        case 'ready':
             if (win.voyagerIframe.src && win.voyagerIframe.src !== 'about:blank' && win.voyagerIframe.src !== VOYAGER_HOME_URL) {
                win.voyagerIframe.style.display = 'block';
            } else {
                 win.voyagerIframe.style.display = 'block';
            }
            break;
    }
}


function createVoyagerMenuDropDownItem(options: {
    text?: string;
    onClick?: (e: MouseEvent) => void;
    disabled?: boolean;
    isSeparator?: boolean;
    isToggle?: boolean;
    isChecked?: () => boolean;
    className?: string;
    actionKey?: string;
}): HTMLDivElement {
    const item = document.createElement('div');
    if (options.isSeparator) {
        item.className = 'voyager-menu-separator';
        return item;
    }

    item.className = 'voyager-menu-item cursor-hand';
    if (options.className) item.classList.add(options.className);
    if (options.actionKey) item.dataset.action = options.actionKey;

    if (options.isToggle && options.isChecked && options.isChecked()) {
        item.innerHTML = `<span class="voyager-menu-checkmark">✓</span> ${options.text}`;
    } else {
        item.textContent = options.text || '';
    }

    if (options.disabled) {
        item.classList.add('disabled');
    } else if (options.onClick) {
        item.onclick = (e) => {
            playSound('click');
            e.stopPropagation();
            options.onClick!(e);
            document.querySelectorAll('.voyager-menu-dropdown.active').forEach(dd => dd.classList.remove('active'));
        };
    }
    return item;
}

function showVoyagerAboutDialog(win: WiniaWindow) {
    const dialogId = 'voyager-about-dialog';
    if (document.getElementById(dialogId)) return;

    const dialog = document.createElement('div');
    dialog.id = dialogId;
    dialog.className = 'winia-dialog';
    dialog.innerHTML = `
        <div class="winia-dialog-title-bar">
            <span>About Web Voyager</span>
        </div>
        <div class="winia-dialog-content">
            <img src="${ICONS.WEB_VOYAGER}" alt="Web Voyager Icon" style="width:32px; height:32px; float:left; margin-right:10px;">
            <p><strong>Web Voyager</strong><br>Version 1.0 (Winia Edition)</p>
            <p>Your retro gateway to the web!</p>
            <button id="voyager-about-ok" class="cursor-hand">OK</button>
        </div>
    `;
    win.element.appendChild(dialog);
    const okButton = document.getElementById('voyager-about-ok');
    okButton?.addEventListener('click', () => { playSound('click'); dialog.remove(); });
    okButton?.focus();
}


function buildVoyagerMenu(
    win: WiniaWindow,
    appData: GeneratedApp,
    elements: {
        iframe: HTMLIFrameElement,
        addressInput: HTMLInputElement,
        navigateToUrl: (url: string) => void
    }
): HTMLElement {
    const menuBar = document.createElement('div');
    menuBar.className = 'voyager-menu-bar';
    win.voyagerMenuBar = menuBar;

    let activeDropdown: HTMLElement | null = null;

    const createMenu = (title: string, items: HTMLDivElement[]) => {
        const menuContainer = document.createElement('div');
        menuContainer.className = 'voyager-menu';

        const titleButton = document.createElement('button');
        titleButton.className = 'voyager-menu-title-button cursor-hand';
        titleButton.textContent = title;
        menuContainer.appendChild(titleButton);

        const dropdown = document.createElement('div');
        dropdown.className = 'voyager-menu-dropdown';
        items.forEach(item => dropdown.appendChild(item));
        menuContainer.appendChild(dropdown);

        const openDropdown = () => {
            if (activeDropdown && activeDropdown !== dropdown) {
                activeDropdown.classList.remove('active');
            }
            dropdown.classList.add('active');
            activeDropdown = dropdown;
        };

        const closeDropdown = () => {
            dropdown.classList.remove('active');
            if (activeDropdown === dropdown) {
                activeDropdown = null;
            }
        };

        titleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            playSound('click');
            if (dropdown.classList.contains('active')) {
                closeDropdown();
            } else {
                openDropdown();
            }
        });

        titleButton.addEventListener('mouseenter', () => {
            if (activeDropdown && activeDropdown !== dropdown) {
                 openDropdown();
            }
        });

        menuBar.appendChild(menuContainer);
        return dropdown;
    };

    createMenu('File', [
        createVoyagerMenuDropDownItem({ text: 'New Window', onClick: () => launchAppById(webVoyagerAppId) }),
        createVoyagerMenuDropDownItem({ text: 'Open File...', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Save As...', disabled: true }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Print...', onClick: () => {
            try { elements.iframe.contentWindow?.print(); } catch (e) { alert("Could not print. " + (e as Error).message); playSound('error'); }
        }}),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Work Offline', isToggle: true, isChecked: () => !!win.isOffline, onClick: () => {
            win.isOffline = !win.isOffline;
            alert(win.isOffline ? "Web Voyager is now offline." : "Web Voyager is now online.");
            const itemEl = menuBar.querySelector('[data-action="file-work-offline"]') as HTMLElement;
            if(itemEl) {
                itemEl.classList.toggle('checked', win.isOffline);
                itemEl.innerHTML = `${win.isOffline ? '<span class="voyager-menu-checkmark">✓</span> ' : ''}Work Offline`;
            }
        }, actionKey: 'file-work-offline'}),
        createVoyagerMenuDropDownItem({ text: 'Close', onClick: () => closeWindow(win.id) }),
    ]);

    createMenu('Edit', [
        createVoyagerMenuDropDownItem({ text: 'Undo', disabled: true }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Cut', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Copy', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Paste', disabled: true }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Select All', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Find in Page...', disabled: true }),
    ]);

    const viewMenuDropdown = createMenu('View', [
        createVoyagerMenuDropDownItem({ text: 'Toolbar', isToggle: true, isChecked: () => win.voyagerToolbarElement?.style.display !== 'none', onClick: (e) => {
            const visible = win.voyagerToolbarElement?.style.display !== 'none';
            if(win.voyagerToolbarElement) win.voyagerToolbarElement.style.display = visible ? 'none' : '';
            (e.currentTarget as HTMLElement).classList.toggle('checked', !visible);
            (e.currentTarget as HTMLElement).innerHTML = `${!visible ? '<span class="voyager-menu-checkmark">✓</span> ' : ''}Toolbar`;
        }, actionKey: 'view-toolbar' }),
        createVoyagerMenuDropDownItem({ text: 'Status Bar', isToggle: true, isChecked: () => win.voyagerStatusBarElement?.style.display !== 'none', onClick: (e) => {
            const visible = win.voyagerStatusBarElement?.style.display !== 'none';
            if(win.voyagerStatusBarElement) win.voyagerStatusBarElement.style.display = visible ? 'none' : '';
            (e.currentTarget as HTMLElement).classList.toggle('checked', !visible);
            (e.currentTarget as HTMLElement).innerHTML = `${!visible ? '<span class="voyager-menu-checkmark">✓</span> ' : ''}Status Bar`;
        }, actionKey: 'view-statusbar' }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Page Source', onClick: () => {
            const currentUrl = elements.addressInput.value;
            if (currentUrl && !currentUrl.startsWith('about:') && !currentUrl.startsWith('data:')) {
                launchAppById(webVoyagerAppId, { newVoyagerUrl: `view-source:${currentUrl}` });
            } else {
                alert("Cannot view source for this type of page."); playSound('error');
            }
        }}),
        createVoyagerMenuDropDownItem({ text: 'Full Screen', onClick: () => toggleMaximizeWindow(win.id) }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'Zoom In', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Zoom Out', disabled: true }),
        createVoyagerMenuDropDownItem({ text: 'Reset Zoom', disabled: true }),
    ]);


    createMenu('Go', [
        createVoyagerMenuDropDownItem({ text: 'Back', actionKey: 'go-back', onClick: () => {
             try { elements.iframe.contentWindow?.history.back(); playSound('voyager_navigate'); } catch(e) {/*ignore*/}
        }}),
        createVoyagerMenuDropDownItem({ text: 'Forward', actionKey: 'go-forward', onClick: () => {
             try { elements.iframe.contentWindow?.history.forward(); playSound('voyager_navigate'); } catch(e) {/*ignore*/}
        }}),
        createVoyagerMenuDropDownItem({ text: 'Home', onClick: () => elements.navigateToUrl(VOYAGER_HOME_URL) }),
        createVoyagerMenuDropDownItem({ isSeparator: true }),
        createVoyagerMenuDropDownItem({ text: 'History (Coming Soon)', disabled: true }),
    ]);

    const bookmarksMenuDropdown = createMenu('Bookmarks', []);

    const rebuildBookmarksMenu = () => {
        bookmarksMenuDropdown.innerHTML = '';
        bookmarksMenuDropdown.appendChild(createVoyagerMenuDropDownItem({
            text: 'Add Bookmark...',
            onClick: () => {
                const pageTitle = elements.iframe.contentDocument?.title || elements.addressInput.value;
                const name = prompt('Enter bookmark name:', pageTitle);
                const url = elements.addressInput.value;
                if (name && url) {
                    win.voyagerBookmarks = win.voyagerBookmarks || [];
                    win.voyagerBookmarks.push({ name, url });
                    rebuildBookmarksMenu();
                }
            }
        }));
        bookmarksMenuDropdown.appendChild(createVoyagerMenuDropDownItem({ text: 'Manage Bookmarks...', disabled: true }));
        bookmarksMenuDropdown.appendChild(createVoyagerMenuDropDownItem({ isSeparator: true }));

        if (win.voyagerBookmarks && win.voyagerBookmarks.length > 0) {
            win.voyagerBookmarks.forEach(bm => {
                bookmarksMenuDropdown.appendChild(createVoyagerMenuDropDownItem({ text: bm.name, onClick: () => elements.navigateToUrl(bm.url) }));
            });
        } else {
            bookmarksMenuDropdown.appendChild(createVoyagerMenuDropDownItem({ text: '(No bookmarks yet)', disabled: true }));
        }
    };
    rebuildBookmarksMenu();

    createMenu('Help', [
        createVoyagerMenuDropDownItem({ text: 'About Web Voyager', onClick: () => showVoyagerAboutDialog(win) }),
        createVoyagerMenuDropDownItem({ text: 'Help Contents', disabled: true }),
    ]);

    document.addEventListener('click', (e) => {
        if (!menuBar.contains(e.target as Node)) {
            if(activeDropdown) {
                activeDropdown.classList.remove('active');
                activeDropdown = null;
            }
        }
    }, true);


    return menuBar;
}


function createEnhancedWebVoyagerContent(win: WiniaWindow, appData: GeneratedApp): HTMLElement {
    const container = document.createElement('div');
    container.id = 'voyager-app-container';
    container.style.cssText = 'width:100%; height:100%; background-color:var(--winia-bg-color); padding:0; box-sizing:border-box; display:flex; flex-direction:column; font-family:Tahoma, sans-serif; font-size:12px;';

    const styleElement = document.createElement('style');
    styleElement.textContent = `
        #voyager-app-container { color: #000; }
        .voyager-toolbar { display: flex; gap: 3px; padding: 3px; border-bottom: 1px solid var(--winia-button-shadow); background-color: var(--winia-bg-color); align-items: center; }
        .voyager-toolbar button { padding: 2px 3px; }
        .voyager-toolbar button img { width: 14px; height: 14px; }
        #voyager-address-bar-container { display: flex; gap: 4px; padding: 3px; border-bottom: 1px solid var(--winia-button-shadow); background-color: var(--winia-bg-color); align-items: center; flex-grow:0; }
        #voyager-address-bar-container label { white-space: nowrap; padding: 0 2px; }
        #voyager-url-input { flex-grow: 1; padding: 3px 4px; border: 2px inset var(--winia-button-shadow); background-color: #FFF; font-family: "MS Sans Serif", Arial, sans-serif; }
        #voyager-go-button { padding: 3px 10px; border: 2px outset var(--winia-button-highlight); background-color: var(--winia-bg-color); }
        #voyager-go-button:active { border-style: inset; padding: 4px 9px 2px 11px; }
        .voyager-content-wrapper { flex-grow: 1; position: relative; background-color: #FFF; border: 2px inset var(--winia-button-shadow); overflow: hidden; margin: 0 2px 2px 2px;}
        #voyager-content-frame { width: 100%; height: 100%; border: none; background-color: #FFF; }
        .voyager-status-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; background-color: rgba(192,192,192,0.9); font-size: 14px; text-align: center; padding: 20px; box-sizing: border-box; z-index:10; }
        .voyager-status-overlay.hidden { display: none; }
        .voyager-error-display { color: var(--winia-error-text); }
    `;
    container.appendChild(styleElement);

    win.voyagerBookmarks = win.voyagerBookmarks || [];
    win.voyagerHistory = win.voyagerHistory || [];

    const addressInput = document.createElement('input');
    addressInput.type = 'text';
    addressInput.id = 'voyager-url-input';
    addressInput.className = 'cursor-text';
    addressInput.placeholder = 'Enter URL (e.g., example.com)';
    addressInput.setAttribute('aria-label', 'Web Address');
    addressInput.setAttribute('autocomplete', 'off');
    addressInput.setAttribute('autocorrect', 'off');
    addressInput.setAttribute('autocapitalize', 'off');
    addressInput.setAttribute('spellcheck', 'false');
    win.voyagerAddressInput = addressInput;

    const iframe = document.createElement('iframe');
    iframe.id = 'voyager-content-frame';
    iframe.sandbox.add('allow-scripts', 'allow-same-origin', 'allow-forms', 'allow-popups', 'allow-modals', 'allow-top-navigation-by-user-activation');
    win.voyagerIframe = iframe;

    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'voyager-status-overlay voyager-loading-indicator hidden';
    loadingIndicator.innerHTML = `
        <dotlottie-player
          src="https://lottie.host/c78059cc-6a73-4c6b-b27e-4045fbd32c48/djnodLLBVT.lottie"
          background="transparent"
          speed="1"
          style="width: 150px; height: 150px;"
          loop
          autoplay
        ></dotlottie-player>
        <p>Loading...</p>
    `;
    win.voyagerLoadingIndicator = loadingIndicator;

    const errorDisplay = document.createElement('div');
    errorDisplay.className = 'voyager-status-overlay voyager-error-display hidden';
    win.voyagerErrorDisplay = errorDisplay;

    const statusBar = document.createElement('div');
    statusBar.className = 'voyager-status-bar';
    win.voyagerStatusBar = statusBar;
    win.voyagerStatusBarElement = statusBar;

    const navigateToUrl = (urlToLoad: string) => {
        let finalUrl = urlToLoad.trim();
        if (!finalUrl) return;

        if (!finalUrl.match(/^[a-zA-Z]+:\/\//i) && !finalUrl.startsWith('data:') && !finalUrl.startsWith('about:')) {
            finalUrl = 'https://' + finalUrl;
        }
        addressInput.value = finalUrl;

        updateVoyagerDisplayState(win, `Loading ${finalUrl}...`, 'loading');

        if (win.voyagerIframe) {
            win.voyagerIframe.src = 'about:blank';
            setTimeout(() => {
                if (win.voyagerIframe) win.voyagerIframe.src = finalUrl;
                playSound('voyager_navigate');
            }, 0);
        }
    };
    (win as any)._navigateToUrlInternal = navigateToUrl;


    iframe.onload = () => {
        if (iframe.src === 'about:blank' && win.voyagerLoadingIndicator && !win.voyagerLoadingIndicator.classList.contains('hidden')) {
            return;
        }
        updateVoyagerDisplayState(win, "Done", 'done');
        try {
            const pageTitle = iframe.contentDocument?.title || iframe.contentWindow?.document.title || "Untitled Page";
            const currentUrl = addressInput.value || "New Tab";
            const newTitle = (pageTitle && pageTitle !== "about:blank") ? `${appData.title} - ${pageTitle}` : `${appData.title} - ${currentUrl}`;

            win.title = newTitle;
            const titleEl = win.element.querySelector('.winia-window-title');
            if (titleEl) titleEl.textContent = newTitle;
            if (win.taskbarButton) {
                const taskbarTitle = win.taskbarButton.querySelector('span');
                if (taskbarTitle) taskbarTitle.textContent = newTitle;
            }

            if (iframe.contentWindow && iframe.contentWindow.location.href !== 'about:blank') {
                const loadedUrl = iframe.contentWindow.location.href;
                 if (loadedUrl !== addressInput.value) addressInput.value = loadedUrl;
                 if (win.voyagerHistory && !win.voyagerHistory.includes(loadedUrl)) {
                    win.voyagerHistory.push(loadedUrl);
                 }
            }
        } catch (e) {
            console.warn("Could not access iframe title/URL (cross-origin).", e);
        }
    };

    iframe.addEventListener('error', (event) => {
        updateVoyagerDisplayState(win, `Error loading ${addressInput.value || 'page'}.`, 'error', addressInput.value || 'the requested page');
    });

    const menuBar = buildVoyagerMenu(win, appData, { iframe, addressInput, navigateToUrl });
    container.appendChild(menuBar);

    const backButton = createControlButton('Back', ICONS.VOYAGER_BACK, () => {
        try { if(iframe.contentWindow) iframe.contentWindow.history.back(); playSound('voyager_navigate');}
        catch (e) { console.warn("Voyager back error:", e); updateVoyagerDisplayState(win, "Navigation error.", 'error'); }
    }, true);
    const forwardButton = createControlButton('Forward', ICONS.VOYAGER_FORWARD, () => {
        try { if(iframe.contentWindow) iframe.contentWindow.history.forward(); playSound('voyager_navigate');}
        catch (e) { console.warn("Voyager forward error:", e); updateVoyagerDisplayState(win, "Navigation error.", 'error');}
    }, true);
    const refreshButton = createControlButton('Refresh', ICONS.VOYAGER_REFRESH, () => {
        try { if(iframe.contentWindow) iframe.contentWindow.location.reload(); playSound('voyager_navigate');}
        catch (e) { console.warn("Voyager refresh error:", e); updateVoyagerDisplayState(win, "Navigation error.", 'error');}
    }, true);
    const stopButton = createControlButton('Stop', ICONS.VOYAGER_STOP, () => {
        try { if(iframe.contentWindow) iframe.contentWindow.stop(); updateVoyagerDisplayState(win, "Page loading stopped.", 'stopped');}
        catch (e) { console.warn("Voyager stop error:", e); updateVoyagerDisplayState(win, "Could not stop loading.", 'error');}
    }, true);
    win.voyagerStopButton = stopButton; stopButton.disabled = true;
    const homeButton = createControlButton('Home', ICONS.VOYAGER_HOME, () => navigateToUrl(VOYAGER_HOME_URL), true);

    const toolbar = document.createElement('div');
    toolbar.className = 'voyager-toolbar';
    toolbar.append(backButton, forwardButton, stopButton, refreshButton, homeButton);
    win.voyagerToolbarElement = toolbar;
    container.appendChild(toolbar);

    const addressBarContainer = document.createElement('div');
    addressBarContainer.id = 'voyager-address-bar-container';
    const addressLabel = document.createElement('label'); addressLabel.htmlFor = 'voyager-url-input'; addressLabel.textContent = 'Address:';
    const goButton = document.createElement('button');
    goButton.id = 'voyager-go-button';
    goButton.textContent = 'Go';
    goButton.className = 'cursor-hand';
    goButton.onclick = () => navigateToUrl(addressInput.value);
    addressInput.onkeydown = (e) => { if (e.key === 'Enter') goButton.click(); };
    addressBarContainer.append(addressLabel, addressInput, goButton);
    container.appendChild(addressBarContainer);

    const contentWrapper = document.createElement('div');
    contentWrapper.className = 'voyager-content-wrapper';
    contentWrapper.append(iframe, loadingIndicator, errorDisplay);
    container.appendChild(contentWrapper);

    container.appendChild(statusBar);
    updateVoyagerDisplayState(win, 'Ready', 'ready');
    if (win.isOffline) {
        updateVoyagerDisplayState(win, 'Working Offline', 'ready');
    } else {
         setTimeout(() => navigateToUrl(VOYAGER_HOME_URL), 0); // Navigate to home on initial load
    }
    return container;
}


// --- Winia ID Card Creator Stubs ---
function launchWiniaIdCreatorWindow(appData: GeneratedApp) {
    createWindow(
        appData.id,
        appData.title,
        (win, data) => buildWiniaIdCreatorUI(win, data),
        appData.iconDataUrl,
        '480px',
        '520px',
        appData
    );
}

function buildWiniaIdCreatorUI(win: WiniaWindow, appData: GeneratedApp): HTMLElement {
    const container = document.createElement('div');
    container.className = 'winia-id-creator-container';
    container.style.padding = '20px';
    container.style.textAlign = 'center';
    container.style.fontFamily = 'Tahoma, sans-serif';
    container.style.color = '#000';
    container.style.backgroundColor = '#C0C0C0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.boxSizing = 'border-box';

    const title = document.createElement('h2');
    title.textContent = appData.title;
    title.style.marginTop = '0';
    title.style.marginBottom = '20px';
    container.appendChild(title);

    const placeholderText = document.createElement('p');
    placeholderText.textContent = 'Winia ID Card Creator UI - Coming Soon!';
    placeholderText.style.fontSize = '14px';
    container.appendChild(placeholderText);

    return container;
}

// --- System Initialization ---
async function initializeWiniaSystem() {
    console.log("🚀 Initializing Winia System...");

    try {
        await initDB();
        console.log("✅ DB Initialized.");
    } catch (error) {
        console.error("❌ DB Initialization failed:", error);
        throw error;
    }

    let settings: any = {};
    try {
        settings = await getAllWiniaSettingsFromDB().catch(() => ({}));
        systemSoundsEnabled = settings[SETTING_SYSTEM_SOUNDS_ENABLED] !== false;
        ambientMusicEnabled = settings[SETTING_AMBIENT_MUSIC_ENABLED] === true;
        winiaMasterVolume = typeof settings[SETTING_MASTER_VOLUME] === 'number' ? settings[SETTING_MASTER_VOLUME] : 0.7;
        if (initAudio()) updateMasterVolume(winiaMasterVolume);
        console.log("✅ Settings loaded.");
    } catch (error) {
        console.error("❌ Settings loading failed:", error);
    }

    try {
        await applyAllCustomCursorsFromDB();
        console.log("✅ Custom cursors applied.");
    } catch (error) {
        console.error("❌ Custom cursors failed:", error);
    }

    try {
        await ensurePreinstalledAppsInDB();
        console.log("✅ Preinstalled apps ensured.");
    } catch (error) {
        console.error("❌ Preinstalled apps failed:", error);
        throw error;
    }

    try {
        await ensureDefaultFileSystemStructure();
        console.log("✅ Default FS structure ensured.");
    } catch (error) {
        console.error("❌ Default FS structure failed:", error);
    }

    try {
        // Load Desktop Icons Layout (placeholder for now, will integrate with settings later)
        // For now, icons will arrange via flexbox default.
        await loadAndRenderAllAppsFromDB();
        console.log("✅ Apps loaded from DB and rendered.");
    } catch (error) {
        console.error("❌ Apps loading failed:", error);
        throw error;
    }

    try {
        await updateRecycleBinDesktopIcon();
        console.log("✅ Recycle bin icon updated.");
    } catch (error) {
        console.error("❌ Recycle bin update failed:", error);
    }

    try {
        await applyCurrentWallpaper();
        console.log("✅ Wallpaper applied.");
    } catch (error) {
        console.error("❌ Wallpaper failed:", error);
    }

    const showCodeGenie = settings[SETTING_SHOW_CODEGENIE_AVATAR] !== false;
    if (showCodeGenie) {
        CODEGENIE_CHARACTER_HOST_ELEMENT.classList.remove('hidden');
        updateCodeGenieAvatar(CodeGenieAvatarState.IDLE);
    } else {
        CODEGENIE_CHARACTER_HOST_ELEMENT.classList.add('hidden');
    }
    console.log("CodeGenie avatar state set.");

    console.log("Winia System Initialization Complete.");

    // Show welcome sequence with pixel dissolve effects
    showWiniaWelcomeSequence();
}


// --- Global Event Listeners ---
START_BUTTON_ELEMENT.addEventListener('click', toggleStartMenu);
DESKTOP_ELEMENT.addEventListener('click', (e) => {
    if (!(e.target as HTMLElement).closest('.winia-window') && !(e.target as HTMLElement).closest('#winia-start-menu') && !(e.target as HTMLElement).closest('.desktop-icon')) {
        setActiveWindow(null);
    }
    if (!START_MENU_ELEMENT.classList.contains('hidden') && !(e.target as HTMLElement).closest('#winia-start-menu') && !(e.target as HTMLElement).closest('#winia-start-button')) {
        toggleStartMenu();
    }
    removeExplorerContextMenu();
});

CODEGENIE_TRAY_ICON_ELEMENT.addEventListener('click', toggleCodeGenieAssistantInteraction);
CODEGENIE_SPEECH_BUBBLE_INPUT_ELEMENT.addEventListener('keydown', (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
        const sendButton = CODEGENIE_SPEECH_BUBBLE_ACTIONS_ELEMENT.querySelector('button');
        if(sendButton) sendButton.click();
    }
});


// --- Initial Setup ---
updateClock();
setInterval(updateClock, 10000); // Update clock every 10 seconds

initializeWiniaSystem().then(() => {
    console.log("✅ Winia System initialized successfully!");
}).catch(err => {
    console.error("CRITICAL ERROR during Winia System Initialization:", err);
    // Display a fallback error message to the user if everything else fails
    document.body.innerHTML = `<div style="color:white; background:red; padding:20px; font-family:sans-serif; text-align:center;">
        <h1>System Initialization Failed</h1>
        <p>Winia encountered a critical error and cannot start. Please check the console for details.</p>
        <p>${(err as Error).message || 'Unknown error'}</p>
    </div>`;
});

window.addEventListener('DOMContentLoaded', () => {
     const dotLottieScript = document.createElement('script');
     dotLottieScript.src = "https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs";
     dotLottieScript.type = "module";
     document.head.appendChild(dotLottieScript);
});

// Ensure all body-level cursor classes use the base `url()` and not the specific data URL
// This is managed by applyCustomCursorStyle which adds !important
// Global style for default cursor on body
document.body.classList.add(CURSOR_CLASS_MAP.DEFAULT);

// Make the codegenie avatar draggable
if (CODEGENIE_CHARACTER_HOST_ELEMENT) {
    let isDraggingGenie = false;
    let genieOffsetX: number, genieOffsetY: number;
    const genieHost = CODEGENIE_CHARACTER_HOST_ELEMENT;

    genieHost.addEventListener('mousedown', (e: MouseEvent) => {
        // Prevent drag if clicking on bubble or its contents
        if ((e.target as HTMLElement).closest('#codegenie-speech-bubble')) {
            return;
        }
        isDraggingGenie = true;
        const rect = genieHost.getBoundingClientRect();
        genieOffsetX = e.clientX - rect.left;
        genieOffsetY = e.clientY - rect.top;
        genieHost.classList.add('cursor-grabbing');
        document.body.classList.add('cursor-grabbing-global');
        e.preventDefault();
    });

    document.addEventListener('mousemove', (e: MouseEvent) => {
        if (!isDraggingGenie) return;
        let newX = e.clientX - genieOffsetX;
        let newY = e.clientY - genieOffsetY;

        const desktopRect = DESKTOP_ELEMENT.getBoundingClientRect();
        newX = Math.max(0, Math.min(newX, desktopRect.width - genieHost.offsetWidth));
        newY = Math.max(0, Math.min(newY, desktopRect.height - genieHost.offsetHeight));

        genieHost.style.left = `${newX}px`;
        genieHost.style.top = `${newY}px`;
    });

    document.addEventListener('mouseup', () => {
        if (isDraggingGenie) {
            isDraggingGenie = false;
            genieHost.classList.remove('cursor-grabbing');
            document.body.classList.remove('cursor-grabbing-global');
        }
    });
}

// --- System Data Import/Export Functions ---

interface WiniaSystemExport {
    version: string;
    timestamp: string;
    winiaIdCard: WiniaIdCardData;
    apps: GeneratedApp[];
    fsNodes: FSNode[];
    settings: Record<string, any>;
    wallpapers: any[];
    customCursors: any[];
    recycleBin: any[];
}

async function exportWiniaSystemData(): Promise<string> {
    try {
        console.log("🔄 Exporting Winia system data...");

        // Gather all system data
        const [apps, fsNodes, settings, recycleBin] = await Promise.all([
            getAllGeneratedApps(),
            getAllFsNodesFromDB(),
            getAllWiniaSettingsFromDB(),
            getAllRecycleBinItemsFromDB()
        ]);

        // Get current Winia ID Card data
        const winiaIdCard: WiniaIdCardData = {
            winiaIdVersion: "1.0",
            firstName: await getWiniaSetting('winiaIdFirstName') || 'Usuario',
            lastName: await getWiniaSetting('winiaIdLastName') || 'Winia',
            alias: await getWiniaSetting('winiaIdAlias') || undefined,
            salutationGender: await getWiniaSetting('winiaIdGender') || 'neutral',
            cardColor: await getWiniaSetting('winiaIdCardColor') || '#667eea',
            portraitDescription: await getWiniaSetting('winiaIdPortraitDescription') || 'Explorador Digital',
            portraitDataUrl: await getWiniaSetting('winiaIdPortraitDataUrl') || undefined
        };

        // Get custom wallpapers from IndexedDB
        const customWallpapers = await getAllCustomWallpapersFromDB();

        // Get custom cursors
        const customCursors = await getAllCustomCursorsFromDB();

        const exportData: WiniaSystemExport = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            winiaIdCard,
            apps: apps.filter(app => !app.isPreinstalled), // Only export user-created apps
            fsNodes,
            settings,
            wallpapers: customWallpapers,
            customCursors,
            recycleBin
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        console.log("✅ System data exported successfully");
        return jsonString;

    } catch (error) {
        console.error("❌ Error exporting system data:", error);
        throw new Error(`Export failed: ${(error as Error).message}`);
    }
}

async function importWiniaSystemData(jsonData: string, mergeMode: boolean = false): Promise<void> {
    try {
        console.log("🔄 Importing Winia system data...");

        const importData: WiniaSystemExport = JSON.parse(jsonData);

        // Validate import data structure
        if (!importData.version || !importData.winiaIdCard) {
            throw new Error("Invalid Winia export file format");
        }

        if (!mergeMode) {
            // Clear existing data (except preinstalled apps)
            console.log("🗑️ Clearing existing user data...");

            // Remove user-created apps
            const existingApps = await getAllGeneratedApps();
            for (const app of existingApps) {
                if (!app.isPreinstalled) {
                    await deleteGeneratedAppFromDB(app.id);
                }
            }

            // Clear file system (keep default structure)
            await clearUserFileSystemData();

            // Clear recycle bin
            await clearRecycleBinFromDB();
        }

        // Import Winia ID Card data
        console.log("👤 Importing Winia ID Card...");
        await setWiniaSetting('winiaIdFirstName', importData.winiaIdCard.firstName);
        await setWiniaSetting('winiaIdLastName', importData.winiaIdCard.lastName);
        await setWiniaSetting('winiaIdAlias', importData.winiaIdCard.alias);
        await setWiniaSetting('winiaIdGender', importData.winiaIdCard.salutationGender);
        await setWiniaSetting('winiaIdCardColor', importData.winiaIdCard.cardColor);
        await setWiniaSetting('winiaIdPortraitDescription', importData.winiaIdCard.portraitDescription);
        await setWiniaSetting('winiaIdPortraitDataUrl', importData.winiaIdCard.portraitDataUrl);

        // Import apps
        console.log("📱 Importing apps...");
        for (const app of importData.apps) {
            await saveGeneratedApp(app);
        }

        // Import file system nodes
        console.log("📁 Importing file system...");
        for (const node of importData.fsNodes) {
            await createFsNode({ path: node.path, type: node.type, content: node.content || '' });
        }

        // Import settings
        console.log("⚙️ Importing settings...");
        for (const [key, value] of Object.entries(importData.settings)) {
            await setWiniaSetting(key, value);
        }

        // Import custom wallpapers
        if (importData.wallpapers && importData.wallpapers.length > 0) {
            console.log("🖼️ Importing custom wallpapers...");
            for (const wallpaper of importData.wallpapers) {
                await addCustomWallpaperToDB(wallpaper);
            }
        }

        // Import custom cursors
        if (importData.customCursors && importData.customCursors.length > 0) {
            console.log("🖱️ Importing custom cursors...");
            for (const cursor of importData.customCursors) {
                await addCustomCursorToDB(cursor);
            }
        }

        // Import recycle bin items
        if (importData.recycleBin && importData.recycleBin.length > 0) {
            console.log("🗑️ Importing recycle bin...");
            for (const item of importData.recycleBin) {
                await addRecycleBinItemToDB(item);
            }
        }

        console.log("✅ System data imported successfully");

        // Refresh the system
        await refreshSystemAfterImport();

    } catch (error) {
        console.error("❌ Error importing system data:", error);
        throw new Error(`Import failed: ${(error as Error).message}`);
    }
}

async function refreshSystemAfterImport(): Promise<void> {
    try {
        // Reload apps and refresh desktop
        await loadAndRenderAllAppsFromDB();

        // Apply imported settings
        const settings = await getAllWiniaSettingsFromDB();
        systemSoundsEnabled = settings[SETTING_SYSTEM_SOUNDS_ENABLED] !== false;
        ambientMusicEnabled = settings[SETTING_AMBIENT_MUSIC_ENABLED] === true;
        winiaMasterVolume = typeof settings[SETTING_MASTER_VOLUME] === 'number' ? settings[SETTING_MASTER_VOLUME] : 0.7;

        // Apply wallpaper
        await applyCurrentWallpaper();

        // Apply custom cursors
        await applyAllCustomCursorsFromDB();

        // Update recycle bin icon
        await updateRecycleBinDesktopIcon();

        console.log("✅ System refreshed after import");

    } catch (error) {
        console.error("❌ Error refreshing system after import:", error);
    }
}

async function downloadSystemExport(): Promise<void> {
    try {
        const exportData = await exportWiniaSystemData();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `winia-system-export-${timestamp}.json`;

        const blob = new Blob([exportData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        playSound('success');
        showSystemNotification('✅ Datos del sistema exportados correctamente', `Archivo guardado: ${filename}`);

    } catch (error) {
        console.error("❌ Error downloading system export:", error);
        playSound('error');
        showSystemNotification('❌ Error al exportar', `No se pudieron exportar los datos: ${(error as Error).message}`);
    }
}

async function uploadAndImportSystemData(mergeMode: boolean = false): Promise<void> {
    return new Promise((resolve, reject) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.style.display = 'none';

        input.onchange = async (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (!file) {
                reject(new Error('No file selected'));
                return;
            }

            try {
                const text = await file.text();
                await importWiniaSystemData(text, mergeMode);

                playSound('success');
                showSystemNotification('✅ Datos importados correctamente', 'El sistema se ha actualizado con los datos importados');
                resolve();

            } catch (error) {
                console.error("❌ Error importing file:", error);
                playSound('error');
                showSystemNotification('❌ Error al importar', `No se pudieron importar los datos: ${(error as Error).message}`);
                reject(error);
            }
        };

        document.body.appendChild(input);
        input.click();
        document.body.removeChild(input);
    });
}

// Helper functions for database operations
async function deleteGeneratedAppFromDB(appId: string): Promise<void> {
    if (!db) throw new Error("DB not initialized");
    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(APPS_STORE_NAME, "readwrite");
        const store = transaction.objectStore(APPS_STORE_NAME);
        const request = store.delete(appId);
        request.onsuccess = () => resolve();
        request.onerror = (event) => reject((event.target as IDBRequest).error);
    });
}

async function getAllCustomWallpapersFromDB(): Promise<any[]> {
    // This would get custom wallpapers from IndexedDB
    // For now, return empty array as placeholder
    return [];
}

async function addCustomWallpaperToDB(wallpaper: any): Promise<void> {
    // This would add a custom wallpaper to IndexedDB
    // Placeholder implementation
    console.log("Adding custom wallpaper:", wallpaper);
}

async function addCustomCursorToDB(cursor: any): Promise<void> {
    // This would add a custom cursor to IndexedDB
    // Placeholder implementation
    console.log("Adding custom cursor:", cursor);
}

async function getAllRecycleBinItemsFromDB(): Promise<any[]> {
    if (!db) return [];

    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readonly");
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        const request = store.getAll();

        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(request.error);
    });
}

async function addRecycleBinItemToDB(item: any): Promise<void> {
    if (!db) return;

    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readwrite");
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        const request = store.add(item);

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
}

async function clearRecycleBinFromDB(): Promise<void> {
    if (!db) return;

    return new Promise((resolve, reject) => {
        const transaction = db!.transaction(RECYCLE_BIN_STORE_NAME, "readwrite");
        const store = transaction.objectStore(RECYCLE_BIN_STORE_NAME);
        const request = store.clear();

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
    });
}

async function clearUserFileSystemData(): Promise<void> {
    // Clear user-created files but keep default structure
    const allNodes = await getAllFsNodesFromDB();
    const defaultPaths = ['/C:/', '/C:/My Documents', '/C:/Program Files', '/C:/Windows'];

    for (const node of allNodes) {
        const isDefaultPath = defaultPaths.some(path => node.path === path || node.path.startsWith(path + '/'));
        if (!isDefaultPath) {
            await deleteFsNodeLogic(node.path, false); // Don't move to recycle bin during import
        }
    }
}

function showSystemNotification(title: string, message: string): void {
    // Create a simple notification overlay
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-family: 'Segoe UI', sans-serif;
        font-size: 14px;
        z-index: 999999;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        animation: slideInRight 0.3s ease-out;
    `;

    notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
        <div style="opacity: 0.9;">${message}</div>
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 300);
    }, 5000);
}